apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: svc-sa-uts
  name: svc-sa-uts
  namespace: inksdev   #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: svc-sa-uts
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: svc-sa-uts
    spec:
      imagePullSecrets:
        - name: aliyun-docker-hub  #提前在项目下配置访问阿里云的账号密码
      containers:
        - image: $REGISTRY/$DOCKERHUB_NAMESPACE/svc-sa-uts:SNAPSHOT-$BUILD_NUMBER
          imagePullPolicy: Always
          name: svc-sa-uts
          ports:
            - containerPort: 8080
              protocol: TCP
          env: #appconfig模板，网址和数字需加引号转字符
            - name: DATABASE_SER
              value: '192.168.99.240:53308/inksuts' # ！！注意同时修改tid和连接的数据库名！！
            - name: TID
              value: 'inks-tid-uts'
            - name: DATABASE_USER
              value: root
            - name: DATABASE_PD
              value: asd@123456
            - name: LICENSE_KEY
              value: ''
            - name: GRFURL
              value: 'http://dev.inksyun.com:18801'
            - name: INIT_SQL
            - name: WXE_BOT
              value: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b1271f39-fa4d-487a-9324-1fb42f850e6f'
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: svc-sa-uts
  name: svc-sa-uts
  namespace: inksdev
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
      nodePort: 30484
  selector:
    app: svc-sa-uts
  sessionAffinity: None
  type: NodePort
