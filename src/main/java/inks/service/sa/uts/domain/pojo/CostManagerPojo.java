package inks.service.sa.uts.domain.pojo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 成本管理数据POJO
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
public class CostManagerPojo {
    
    /**
     * 成本明细列表
     */
    private List<CostDetailItem> costDetails;
    
    /**
     * 成本明细项
     */
    @Data
    public static class CostDetailItem {
        /** 分类名称 */
        private String designation;
        /** 成本(CNY) */
        private BigDecimal costCny;
        /** BOM百分比 */
        private BigDecimal bomPercent;
        /** EXW百分比 */
        private BigDecimal exwPercent;
        
        public CostDetailItem() {}
        
        public CostDetailItem(String designation, BigDecimal costCny, BigDecimal bomPercent, BigDecimal exwPercent) {
            this.designation = designation;
            this.costCny = costCny;
            this.bomPercent = bomPercent;
            this.exwPercent = exwPercent;
        }
    }
}
