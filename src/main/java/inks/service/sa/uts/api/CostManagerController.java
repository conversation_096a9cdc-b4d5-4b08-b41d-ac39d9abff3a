//package inks.service.sa.uts.api;
//
//import inks.common.core.domain.R;
//import inks.common.core.utils.ServletUtils;
//import inks.service.sa.uts.domain.pojo.CostManagerPojo;
//import inks.service.sa.uts.utils.CostManagerExcelGenerator;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import java.io.OutputStream;
//import java.math.BigDecimal;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 成本管理Excel生成控制器
// *
// * <AUTHOR>
// * @since 2025-01-14
// */
//@RestController
//@RequestMapping("S34M99B1")
//@Api(tags = "S34M99B1:成本管理Excel生成")
//public class CostManagerController {
//
//    /**
//     * 生成成本管理Excel（包含饼图）
//     *
//     * @param data 成本数据
//     * @return 操作结果
//     */
//    @ApiOperation(value = "生成成本管理Excel", notes = "根据提供的成本数据生成包含饼图的Excel文件", produces = "application/json")
//    @PostMapping("/generateCostManagerExcel")
//    public R<String> generateCostManagerExcel(@RequestBody CostManagerPojo data) {
//        try {
//            // 数据验证
//            if (data == null || data.getCostDetails() == null || data.getCostDetails().isEmpty()) {
//                return R.fail("成本数据不能为空");
//            }
//
//            // 生成Excel文件
//            byte[] excelBytes = CostManagerExcelGenerator.generateExcel(data);
//
//            // 生成文件名
//            String fileName = generateFileName();
//
//            // 获取响应对象
//            HttpServletResponse response = ServletUtils.getResponse();
//
//            // 设置响应头
//            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
//            response.setContentLength(excelBytes.length);
//
//            // 写入响应流
//            try (OutputStream os = response.getOutputStream()) {
//                os.write(excelBytes);
//                os.flush();
//            }
//
//            return R.ok("成本管理Excel文件生成成功");
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            return R.fail("Excel文件生成失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 生成示例成本管理Excel
//     *
//     * @return 操作结果
//     */
//    @ApiOperation(value = "生成示例成本管理Excel", notes = "生成包含示例数据的成本管理Excel文件", produces = "application/json")
//    @GetMapping("/generateSampleExcel")
//    public R<String> generateSampleExcel() {
//        try {
//            // 创建示例数据
//            CostManagerPojo sampleData = createSampleData();
//
//            // 生成Excel文件
//            byte[] excelBytes = CostManagerExcelGenerator.generateExcel(sampleData);
//
//            // 生成文件名
//            String fileName = "Sample_" + generateFileName();
//
//            // 获取响应对象
//            HttpServletResponse response = ServletUtils.getResponse();
//
//            // 设置响应头
//            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
//            response.setContentLength(excelBytes.length);
//
//            // 写入响应流
//            try (OutputStream os = response.getOutputStream()) {
//                os.write(excelBytes);
//                os.flush();
//            }
//
//            return R.ok("示例成本管理Excel文件生成成功");
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            return R.fail("示例Excel文件生成失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取数据结构示例
//     *
//     * @return 数据结构示例
//     */
//    @ApiOperation(value = "获取数据结构示例", notes = "返回接口所需的数据结构示例", produces = "application/json")
//    @GetMapping("/getDataExample")
//    public R<CostManagerPojo> getDataExample() {
//        CostManagerPojo example = createSampleData();
//        return R.ok(example);
//    }
//
//    /**
//     * 创建示例数据
//     */
//    private CostManagerPojo createSampleData() {
//        CostManagerPojo data = new CostManagerPojo();
//        List<CostManagerPojo.CostDetailItem> costDetails = new ArrayList<>();
//
//        // 添加成本明细数据
//        costDetails.add(new CostManagerPojo.CostDetailItem("PLASTIC", new BigDecimal("7.039"), new BigDecimal("7"), new BigDecimal("5")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("METAL", new BigDecimal("3.768"), new BigDecimal("4"), new BigDecimal("3")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("ELECTRONIC", new BigDecimal("83.493"), new BigDecimal("83"), new BigDecimal("56")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("ACCESSORIES", new BigDecimal("2.751"), new BigDecimal("3"), new BigDecimal("2")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("PACKAGING", new BigDecimal("3.655"), new BigDecimal("3"), new BigDecimal("2")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("BOM", new BigDecimal("100.706"), new BigDecimal("100"), new BigDecimal("68")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("PROCESSING", new BigDecimal("8.112"), new BigDecimal("0"), new BigDecimal("5")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("FINANCIAL", new BigDecimal("6.53"), new BigDecimal("0"), new BigDecimal("4")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("OVERHEAD", new BigDecimal("8.71"), new BigDecimal("0"), new BigDecimal("6")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("MARGIN", new BigDecimal("23.94"), new BigDecimal("0"), new BigDecimal("16")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("FOM", new BigDecimal("39.17"), new BigDecimal("0"), new BigDecimal("26")));
//        costDetails.add(new CostManagerPojo.CostDetailItem("Total EXW", new BigDecimal("147.992"), new BigDecimal("0"), new BigDecimal("100")));
//
//        data.setCostDetails(costDetails);
//        return data;
//    }
//
//    /**
//     * 生成文件名
//     */
//    private String generateFileName() {
//        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HHmmss"));
//        return "Cost_Manager_" + timestamp + ".xlsx";
//    }
//}
