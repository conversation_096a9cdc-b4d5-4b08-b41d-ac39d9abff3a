package inks.service.sa.uts.utils;

import inks.service.sa.uts.domain.pojo.CostManagerPojo;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xddf.usermodel.XDDFColor;
import org.apache.poi.xddf.usermodel.XDDFColorRgbBinary;
import org.apache.poi.xddf.usermodel.XDDFShapeProperties;
import org.apache.poi.xddf.usermodel.XDDFSolidFillProperties;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * 成本管理Excel生成器（支持真正的饼图）
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
public class CostManagerExcelGenerator {
    
    /**
     * 生成包含饼图的Excel文件
     * 
     * @param data 成本数据
     * @return Excel文件字节数组
     * @throws IOException IO异常
     */
    public static byte[] generateExcel(CostManagerPojo data) throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("Cost_Manager");
            
            // 创建样式
            CellStyle titleStyle = createTitleStyle(workbook);
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            CellStyle percentStyle = createPercentStyle(workbook);
            CellStyle totalStyle = createTotalStyle(workbook);
            
            // 创建标题
            createTitle(sheet, titleStyle);
            
            // 创建成本明细表
            int tableEndRow = createCostDetailTable(sheet, data.getCostDetails(), headerStyle, dataStyle, percentStyle, totalStyle);
            
            // 创建BOM饼图
            createBomPieChart(sheet, workbook, data.getCostDetails(), tableEndRow + 2);
            
            // 创建EXW饼图
            createExwPieChart(sheet, workbook, data.getCostDetails(), tableEndRow + 2);
            
            // 调整列宽
            adjustColumnWidths(sheet);
            
            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }
    
    /**
     * 创建标题
     */
    private static void createTitle(XSSFSheet sheet, CellStyle titleStyle) {
        // 创建DECATHLON标题
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("DECATHLON");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));
        
        // 创建EXW PRICE标题
        Row priceRow = sheet.createRow(1);
        Cell priceCell = priceRow.createCell(0);
        priceCell.setCellValue("EXW PRICE");
        priceCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 4));
    }
    
    /**
     * 创建成本明细表
     */
    private static int createCostDetailTable(XSSFSheet sheet, List<CostManagerPojo.CostDetailItem> costDetails,
                                           CellStyle headerStyle, CellStyle dataStyle, CellStyle percentStyle, CellStyle totalStyle) {
        int startRow = 2;
        
        // 创建表头
        Row headerRow = sheet.createRow(startRow);
        String[] headers = {"DESIGNATION", "COST(CNY)", "% BOM", "% EXW"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // 填充数据
        int rowIndex = startRow + 1;
        BigDecimal totalExw = BigDecimal.ZERO;
        
        for (CostManagerPojo.CostDetailItem item : costDetails) {
            Row dataRow = sheet.createRow(rowIndex);
            
            // 分类名称
            Cell designationCell = dataRow.createCell(0);
            designationCell.setCellValue(item.getDesignation());
            
            // 成本
            Cell costCell = dataRow.createCell(1);
            costCell.setCellValue(item.getCostCny().doubleValue());
            
            // BOM百分比
            Cell bomPercentCell = dataRow.createCell(2);
            bomPercentCell.setCellValue(item.getBomPercent().doubleValue() / 100);
            bomPercentCell.setCellStyle(percentStyle);
            
            // EXW百分比
            Cell exwPercentCell = dataRow.createCell(3);
            exwPercentCell.setCellValue(item.getExwPercent().doubleValue() / 100);
            exwPercentCell.setCellStyle(percentStyle);
            
            // 应用样式
            if ("BOM".equals(item.getDesignation()) || "Total EXW".equals(item.getDesignation())) {
                designationCell.setCellStyle(totalStyle);
                costCell.setCellStyle(totalStyle);
            } else {
                designationCell.setCellStyle(dataStyle);
                costCell.setCellStyle(dataStyle);
            }
            
            // 计算总计
            if (!"BOM".equals(item.getDesignation()) && !"Total EXW".equals(item.getDesignation())) {
                totalExw = totalExw.add(item.getCostCny());
            }
            
            rowIndex++;
        }
        
        return rowIndex - 1;
    }
    
    /**
     * 创建BOM饼图
     */
    private static void createBomPieChart(XSSFSheet sheet, XSSFWorkbook workbook,
                                        List<CostManagerPojo.CostDetailItem> costDetails, int chartStartRow) {
        // 创建绘图区域
        XSSFDrawing drawing = sheet.createDrawingPatriarch();
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 6, chartStartRow, 12, chartStartRow + 15);

        // 创建图表
        XSSFChart chart = drawing.createChart(anchor);
        chart.setTitleText("BOM");

        // 准备数据
        String[] categories = {"ELECTRONIC", "PLASTIC", "METAL", "ACCESSORIES", "PACKAGING"};
        Double[] values = new Double[5];

        // 从数据中提取BOM相关数据
        for (CostManagerPojo.CostDetailItem item : costDetails) {
            String designation = item.getDesignation();
            if ("ELECTRONIC".equals(designation)) {
                values[0] = item.getBomPercent().doubleValue();
            } else if ("PLASTIC".equals(designation)) {
                values[1] = item.getBomPercent().doubleValue();
            } else if ("METAL".equals(designation)) {
                values[2] = item.getBomPercent().doubleValue();
            } else if ("ACCESSORIES".equals(designation)) {
                values[3] = item.getBomPercent().doubleValue();
            } else if ("PACKAGING".equals(designation)) {
                values[4] = item.getBomPercent().doubleValue();
            }
        }

        // 创建数据源
        XDDFCategoryDataSource categoryDataSource = XDDFDataSourcesFactory.fromArray(categories);
        XDDFNumericalDataSource<Double> valuesDataSource = XDDFDataSourcesFactory.fromArray(values);

        // 创建饼图
        XDDFPieChartData pieChartData = (XDDFPieChartData) chart.createData(ChartTypes.PIE, null, null);
        XDDFPieChartData.Series series = (XDDFPieChartData.Series) pieChartData.addSeries(categoryDataSource, valuesDataSource);
        series.setTitle("BOM Distribution", null);

        // 设置数据标签显示分类名称和百分比
        series.setShowCategoryName(true);
        series.setShowPercent(true);
        series.setShowValue(false);
        series.setShowLeaderLines(true);

        // 绘制图表
        chart.plot(pieChartData);

        // 添加图例
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.RIGHT);
    }
    
    /**
     * 创建EXW饼图
     */
    private static void createExwPieChart(XSSFSheet sheet, XSSFWorkbook workbook,
                                        List<CostManagerPojo.CostDetailItem> costDetails, int chartStartRow) {
        // 创建绘图区域
        XSSFDrawing drawing = sheet.createDrawingPatriarch();
        XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 13, chartStartRow, 19, chartStartRow + 15);

        // 创建图表
        XSSFChart chart = drawing.createChart(anchor);
        chart.setTitleText("EXW");

        // 准备数据
        String[] categories = {"ELECTRONIC", "FOM", "PROCESSING", "PLASTIC", "METAL", "ACCESSORIES"};
        Double[] values = new Double[6];

        // 从数据中提取EXW相关数据
        for (CostManagerPojo.CostDetailItem item : costDetails) {
            String designation = item.getDesignation();
            if ("ELECTRONIC".equals(designation)) {
                values[0] = item.getExwPercent().doubleValue();
            } else if ("FOM".equals(designation)) {
                values[1] = item.getExwPercent().doubleValue();
            } else if ("PROCESSING".equals(designation)) {
                values[2] = item.getExwPercent().doubleValue();
            } else if ("PLASTIC".equals(designation)) {
                values[3] = item.getExwPercent().doubleValue();
            } else if ("METAL".equals(designation)) {
                values[4] = item.getExwPercent().doubleValue();
            } else if ("ACCESSORIES".equals(designation)) {
                values[5] = item.getExwPercent().doubleValue();
            }
        }

        // 创建数据源
        XDDFCategoryDataSource categoryDataSource = XDDFDataSourcesFactory.fromArray(categories);
        XDDFNumericalDataSource<Double> valuesDataSource = XDDFDataSourcesFactory.fromArray(values);

        // 创建饼图
        XDDFPieChartData pieChartData = (XDDFPieChartData) chart.createData(ChartTypes.PIE, null, null);
        XDDFPieChartData.Series series = (XDDFPieChartData.Series) pieChartData.addSeries(categoryDataSource, valuesDataSource);
        series.setTitle("EXW Distribution", null);

        // 设置数据标签显示分类名称和百分比
        series.setShowCategoryName(true);
        series.setShowPercent(true);
        series.setShowValue(false);
        series.setShowLeaderLines(true);

        // 绘制图表
        chart.plot(pieChartData);

        // 添加图例
        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.RIGHT);
    }
    
    /**
     * 调整列宽
     */
    private static void adjustColumnWidths(XSSFSheet sheet) {
        for (int i = 0; i < 20; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建标题样式
     */
    private static CellStyle createTitleStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 16);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);

        // 设置背景色为蓝色
        style.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    /**
     * 创建表头样式
     */
    private static CellStyle createHeaderStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        // 设置背景色为黄色
        style.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    /**
     * 创建数据样式
     */
    private static CellStyle createDataStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 9);
        style.setFont(font);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    /**
     * 创建百分比样式
     */
    private static CellStyle createPercentStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 9);
        style.setFont(font);

        // 设置百分比格式
        style.setDataFormat(workbook.createDataFormat().getFormat("0%"));

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    /**
     * 创建总计样式
     */
    private static CellStyle createTotalStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 9);
        style.setFont(font);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }
}
