package inks.service.sa.uts.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.uts.config.constant.JdbcConstants;
import inks.service.sa.uts.config.constant.MyConstant;
import inks.service.sa.uts.domain.UtsFreereportsEntity;
import inks.service.sa.uts.domain.UtsFreereportsitemEntity;
import inks.service.sa.uts.domain.database.DataSource;
import inks.service.sa.uts.domain.database.DataSourceDto;
import inks.service.sa.uts.domain.database.SetTypeEnum;
import inks.service.sa.uts.domain.pojo.UtsDatabasePojo;
import inks.service.sa.uts.domain.pojo.UtsFreereportsPojo;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemdetailPojo;
import inks.service.sa.uts.mapper.UtsFreereportsMapper;
import inks.service.sa.uts.mapper.UtsFreereportsitemMapper;
import inks.service.sa.uts.service.JdbcService;
import inks.service.sa.uts.service.UtsDatabaseService;
import inks.service.sa.uts.service.UtsFreereportsService;
import inks.service.sa.uts.service.UtsFreereportsitemService;
import inks.service.sa.uts.utils.PrintColor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 自由报表(UtsFreereports)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-14 10:41:54
 */
@Service("utsFreereportsService")
public class UtsFreereportsServiceImpl implements UtsFreereportsService {
    private static final Logger log = LoggerFactory.getLogger(UtsFreereportsServiceImpl.class);

    @Resource
    private UtsFreereportsMapper utsFreereportsMapper;

    @Resource
    private UtsFreereportsitemMapper utsFreereportsitemMapper;


    @Resource
    private UtsFreereportsitemService utsFreereportsitemService;
    @Resource
    private UtsDatabaseService utsDatabaseService;
    @Resource
    private JdbcService jdbcService;

    @Override
    public UtsFreereportsPojo getEntity(String key, String tid) {
        return this.utsFreereportsMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<UtsFreereportsitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsFreereportsitemdetailPojo> lst = utsFreereportsMapper.getPageList(queryParam);
            PageInfo<UtsFreereportsitemdetailPojo> pageInfo = new PageInfo<UtsFreereportsitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public UtsFreereportsPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            UtsFreereportsPojo utsFreereportsPojo = this.utsFreereportsMapper.getEntity(key, tid);
            //读取子表
            utsFreereportsPojo.setItem(utsFreereportsitemMapper.getList(utsFreereportsPojo.getId(), tid));
            return utsFreereportsPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<UtsFreereportsPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsFreereportsPojo> lst = utsFreereportsMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (UtsFreereportsPojo item : lst) {
                item.setItem(utsFreereportsitemMapper.getList(item.getId(), tid));
            }
            PageInfo<UtsFreereportsPojo> pageInfo = new PageInfo<UtsFreereportsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<UtsFreereportsPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsFreereportsPojo> lst = utsFreereportsMapper.getPageTh(queryParam);
            PageInfo<UtsFreereportsPojo> pageInfo = new PageInfo<UtsFreereportsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public UtsFreereportsPojo insert(UtsFreereportsPojo utsFreereportsPojo) {
        String tid = utsFreereportsPojo.getTenantid();
        // 报表编码唯一校验
        if (this.utsFreereportsMapper.countReportcode(utsFreereportsPojo.getReportcode(), null, tid) > 0) {
            throw new BaseBusinessException("报表编码已存在");
        }
        //LocalMark 校验 =1时,必须设置主表名,拼接tid
        String maintable = utsFreereportsPojo.getMaintable();
        if (Objects.equals(utsFreereportsPojo.getLocalmark(), 1)) {
            if (isBlank(maintable)) {
                throw new BaseBusinessException("主表名不能为空");
            }
            if (!utsFreereportsPojo.getSqlfrom().contains(maintable)) {
                throw new BaseBusinessException("主表名不在SqlFrom中");
            }
        }
        // 校验SQL语句 必须是查询语句
        String dynSentence = utsFreereportsPojo.getDynsentence();
        if ("sql".equals(utsFreereportsPojo.getDyntype()) && isNotBlank(dynSentence)) {
            JdbcServiceImpl.validateSqlSelect(dynSentence);
        }
        //初始化NULL字段
        cleanNull(utsFreereportsPojo);
        //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        UtsFreereportsEntity utsFreereportsEntity = new UtsFreereportsEntity();
        BeanUtils.copyProperties(utsFreereportsPojo, utsFreereportsEntity);

        //设置id和新建日期
        utsFreereportsEntity.setId(id);
        utsFreereportsEntity.setRevision(1);  //乐观锁
        // 如果传入了连接池id,并且SqlFull是空或者AUTO开头的,则根据数据库类型拼接完整SQL
        String databaseid = utsFreereportsPojo.getDatabaseid();
        String sqlFull = utsFreereportsPojo.getSqlfull();
        if (isNotBlank(databaseid) && (isBlank(sqlFull) || sqlFull.startsWith("AUTO"))) {
            String databaseType = utsDatabaseService.getUrlDatabaseType(databaseid, tid);
            utsFreereportsEntity.setSqlfull("AUTO:" + buildSqlFull(utsFreereportsPojo, databaseType));
        }
        //插入主表
        this.utsFreereportsMapper.insert(utsFreereportsEntity);
        //Item子表处理
        List<UtsFreereportsitemPojo> lst = utsFreereportsPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (UtsFreereportsitemPojo item : lst) {
                //初始化item的NULL
                UtsFreereportsitemPojo itemPojo = this.utsFreereportsitemService.clearNull(item);
                UtsFreereportsitemEntity utsFreereportsitemEntity = new UtsFreereportsitemEntity();
                BeanUtils.copyProperties(itemPojo, utsFreereportsitemEntity);
                //设置id和Pid
                utsFreereportsitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                utsFreereportsitemEntity.setPid(id);
                utsFreereportsitemEntity.setTenantid(tid);
                utsFreereportsitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.utsFreereportsitemMapper.insert(utsFreereportsitemEntity);
            }

        }
        //返回Bill实例
        return this.getBillEntity(utsFreereportsEntity.getId(), tid);
    }


    @Override
    @Transactional
    public UtsFreereportsPojo update(UtsFreereportsPojo utsFreereportsPojo) {
        String tid = utsFreereportsPojo.getTenantid();
        // 报表编码唯一校验
        if (this.utsFreereportsMapper.countReportcode(utsFreereportsPojo.getReportcode(), utsFreereportsPojo.getId(), tid) > 0) {
            throw new BaseBusinessException("报表编码已存在");
        }
        //LocalMark 校验 =1时,必须设置主表名,拼接tid
        String maintable = utsFreereportsPojo.getMaintable();
        if (Objects.equals(utsFreereportsPojo.getLocalmark(), 1)) {
            if (isBlank(maintable)) {
                throw new BaseBusinessException("主表名不能为空");
            }
            if (!utsFreereportsPojo.getSqlfrom().contains(maintable)) {
                throw new BaseBusinessException("主表名不在SqlFrom中");
            }
        }
        // 校验SQL语句 必须是查询语句
        String dynSentence = utsFreereportsPojo.getDynsentence();
        if ("sql".equals(utsFreereportsPojo.getDyntype()) && isNotBlank(dynSentence)) {
            JdbcServiceImpl.validateSqlSelect(dynSentence);
        }
        //主表更改
        UtsFreereportsEntity utsFreereportsEntity = new UtsFreereportsEntity();
        BeanUtils.copyProperties(utsFreereportsPojo, utsFreereportsEntity);
        // 如果传入了连接池id,并且SqlFull是空或者AUTO开头的,则根据数据库类型拼接完整SQL
        String databaseid = utsFreereportsPojo.getDatabaseid();
        String sqlFull = utsFreereportsPojo.getSqlfull();
        if (isNotBlank(databaseid) && (isBlank(sqlFull) || sqlFull.startsWith("AUTO"))) {
            String databaseType = utsDatabaseService.getUrlDatabaseType(databaseid, tid);
            utsFreereportsEntity.setSqlfull("AUTO:" + buildSqlFull(utsFreereportsPojo, databaseType));
        }
        this.utsFreereportsMapper.update(utsFreereportsEntity);
        if (utsFreereportsPojo.getItem() != null) {
            //Item子表处理
            List<UtsFreereportsitemPojo> lst = utsFreereportsPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = utsFreereportsMapper.getDelItemIds(utsFreereportsPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String delId : lstDelIds) {
                    this.utsFreereportsitemMapper.delete(delId, tid);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (UtsFreereportsitemPojo item : lst) {
                    UtsFreereportsitemEntity utsFreereportsitemEntity = new UtsFreereportsitemEntity();
                    if ("".equals(item.getId()) || item.getId() == null) {
                        //初始化item的NULL
                        UtsFreereportsitemPojo itemPojo = this.utsFreereportsitemService.clearNull(item);
                        BeanUtils.copyProperties(itemPojo, utsFreereportsitemEntity);
                        //设置id和Pid
                        utsFreereportsitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        utsFreereportsitemEntity.setPid(utsFreereportsEntity.getId());  // 主表 id
                        utsFreereportsitemEntity.setTenantid(tid);   // 租户id
                        utsFreereportsitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.utsFreereportsitemMapper.insert(utsFreereportsitemEntity);
                    } else {
                        BeanUtils.copyProperties(item, utsFreereportsitemEntity);
                        utsFreereportsitemEntity.setTenantid(tid);
                        this.utsFreereportsitemMapper.update(utsFreereportsitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(utsFreereportsEntity.getId(), tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
        UtsFreereportsPojo utsFreereportsPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<UtsFreereportsitemPojo> lst = utsFreereportsPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (UtsFreereportsitemPojo item : lst) {
                this.utsFreereportsitemMapper.delete(item.getId(), tid);
            }
        }
        return this.utsFreereportsMapper.delete(key, tid);
    }

    @Override
    public List<Map<String, Object>> select(String key, String tid) {
        UtsFreereportsPojo freeReportDB = utsFreereportsMapper.getEntity(key, tid);
        // 查询的SQL的结构是
        // SELECT...(TOP)...FROM...WHERE...GROUP BY...HAVING...ORDER BY...(LIMIT)...
        // 获取数据库连接和数据库名字
        String databaseid = freeReportDB.getDatabaseid();
        if (isBlank(databaseid)) throw new BaseBusinessException("未绑定数据库连接");
        Map<String, Object> dataSourceAndName = utsDatabaseService.getDataSource(databaseid, tid);
        javax.sql.DataSource dataSource = (javax.sql.DataSource) dataSourceAndName.get("dataSource");
        // String databaseName = (String) dataSourceAndName.get("databaseName");
        String databaseType = (String) dataSourceAndName.get("databaseType");
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
        // 构建查询参数
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        // 获取自由报表本身拼接的完整SQL 如果DynType是sql 则判断DynSentence是否有完整SQL语句
        String sqlFull = "";
        String dynType = freeReportDB.getDyntype();
        String dynSentence = freeReportDB.getDynsentence();
        if ("sql".equals(dynType) && isNotBlank(dynSentence)) {
            sqlFull = dynSentence;
        } else if ("http".equals(dynType)) {
            // TODO 如果DynType是http 则调用http接口获取数据
        } else {
            sqlFull = buildSqlFull(freeReportDB, databaseType);
        }
        // 打印执行语句
        PrintColor.zi("打印执行语句: " + sqlFull);
        // 执行查询并返回结果
        List<Map<String, Object>> resultMaps = jdbcTemplate.queryForList(sqlFull, parameters);
        return resultMaps;
    }


    @Override
    public Object selectEdb(String key, Map<String, Object> params, String datapath, String tid) {
        // 自由报表(Edb数据集)
        UtsFreereportsPojo freeReportsDB = utsFreereportsMapper.getEntity(key, tid);
        // 最终执行的SQL 需要 替换SQL中的占位符 ${key} 为实际的值！！
        String dyntype = freeReportsDB.getDyntype();
        String dynSentenceNew = freeReportsDB.getDynsentence();// 当dynType为sql时，dynSentence为完整SQL
        dynSentenceNew = replaceSQL(dynSentenceNew, params);// 替换占位符

        // ================= 执行SQL、HTTP查询 =================
        List<JSONObject> resultSqlOrHttp = getResultSqlOrHttp(freeReportsDB, dynSentenceNew, datapath, params, tid);
        return resultSqlOrHttp;
    }


    // params是前端传入的查询参数，举例：{
    //  "PageNum": 1,
    //  "PageSize": 20,
    //  "DateRange": {
    //    "StartDate": "2023-11-01 00:00:00",
    //    "EndDate": "2023-11-30 23:59:59"
    //  },
    //  "scenedata": [
    //    {
    //      "field": "App_Workgroup.Seller",
    //      "fieldtype": 1,
    //      "math": "equal",
    //      "value": "李四"
    //    }
    //  ]
    //}
    public Object pageListEdb(String reportcode, String reportid, Map<String, Object> params, String datapath, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        // 将 pagenum 和 pagesize 从 Object 类型转换为 Integer
        int pageNum = params.get("PageNum") != null ? Integer.parseInt(params.get("PageNum").toString()) : 1; // 默认页码为1
        int pageSize = params.get("PageSize") != null ? Integer.parseInt(params.get("PageSize").toString()) : 10; // 默认页面大小为10
        // reportcode和key只有一个有值，优先key
        UtsFreereportsPojo freeReportsDB = null;
        if (isNotBlank(reportid)) {
            // 自由报表(Edb数据集)
            freeReportsDB = utsFreereportsMapper.getEntity(reportid, tid);
        } else if (isNotBlank(reportcode)) {
            freeReportsDB = utsFreereportsMapper.getEntityByReportcode(reportcode, tid);
        }
        // 最终执行的SQL 需要 替换SQL中的占位符 ${key} 为实际的值！！
        String dyntype = freeReportsDB.getDyntype();
        String dynSentenceNew = freeReportsDB.getDynsentence();// 当dynType为sql时，dynSentence为完整SQL
        dynSentenceNew = replaceSQL(dynSentenceNew, params);// 替换占位符

        // ================= 根据类型选择执行方式 =================
        // 如果是http类型，直接执行HTTP查询
        if ("http".equals(dyntype)) {
            List<JSONObject> resultSqlOrHttp = getResultSqlOrHttp(freeReportsDB, dynSentenceNew, datapath, params, tid);
            return resultSqlOrHttp;
        }
        // 如果是sql类型，使用优化的数据库分页（避免重复查询）
        else if ("sql".equals(dyntype)) {
            try {
                // 获取数据库连接信息
                String databaseId = freeReportsDB.getDatabaseid();
                if (isBlank(databaseId)) {
                    throw new BaseBusinessException("未绑定数据库连接");
                }

                // 获取数据源和数据库类型（一次性获取，避免重复调用）
                Map<String, Object> dataSourceAndName = utsDatabaseService.getDataSource(databaseId, tid);
                javax.sql.DataSource dataSource = (javax.sql.DataSource) dataSourceAndName.get("dataSource");
                String databaseType = (String) dataSourceAndName.get("databaseType");

                log.info("开始数据库分页查询 - 数据库类型: {}, 页码: {}, 页大小: {}", databaseType, pageNum, pageSize);

                // 使用优化的getTotalCount方法获取真实总数
                int total = getTotalCount(dynSentenceNew, dataSource, databaseType);

                if (total < 0) {
                    log.warn("总数查询失败，降级到内存分页");
                    List<JSONObject> allResults = getResultSqlOrHttp(freeReportsDB, dynSentenceNew, datapath, params, tid);
                    return fallbackToMemoryPaging(allResults, pageNum, pageSize);
                }

                if (total == 0) {
                    log.info("总记录数为0，返回空列表");
                    return buildPageInfo(Collections.emptyList(), 0, pageNum, pageSize);
                }

                log.info("数据库分页 - 总记录数: {}", total);

                // 构建优化的分页SQL
                String pagedSql = buildPagedSql(dynSentenceNew, databaseType, pageNum, pageSize);
                log.info("构建分页SQL完成");

                // 直接执行分页查询，避免通过getResultSqlOrHttp重复处理
                DataSourceDto pagedDto = new DataSourceDto();
                pagedDto.setSourceconfig(JSON.toJSONString(getSourceConfig(databaseId, tid)));
                pagedDto.setSourcetype(getSourceType(databaseId, tid));
                pagedDto.setId(databaseId);
                pagedDto.setDynSentence(pagedSql);

                // 添加分页信息到上下文数据，用于日志输出
                Map<String, Object> contextData = new HashMap<>();
                contextData.put("pageNum", pageNum);
                contextData.put("pageSize", pageSize);
                contextData.put("total", total);
                pagedDto.setContextData(contextData);

                // 直接调用executeRelationalDb，避免getResultSqlOrHttp的额外处理
                List<JSONObject> pagedResults = utsDatabaseService.execute(pagedDto);

                // 转换为Map列表
                List<Map<String, Object>> resultList = new ArrayList<>();
                for (JSONObject jsonObject : pagedResults) {
                    resultList.add(jsonObject.getInnerMap());
                }

                log.info("数据库分页完成 - 返回数据条数: {}, 总记录数: {}", resultList.size(), total);
                return buildPageInfo(resultList, total, pageNum, pageSize);

            } catch (Exception e) {
                log.error("数据库分页查询失败: {}", e.getMessage(), e);
                // 降级到内存分页：先执行原始查询获取所有数据
                log.warn("降级到内存分页模式");
                List<JSONObject> allResults = getResultSqlOrHttp(freeReportsDB, dynSentenceNew, datapath, params, tid);
                return fallbackToMemoryPaging(allResults, pageNum, pageSize);
            }
        } else {
            return "类型异常";
        }
    }

    /**
     * 构建分页SQL - 兼容老版本SQL Server，支持虚拟分页
     */
    private String buildPagedSql(String originalSql, String databaseType, int pageNum, int pageSize) {
        // 检查原始SQL是否包含TOP/LIMIT限制
        int virtualLimit = extractVirtualLimit(originalSql, databaseType);

        if (virtualLimit > 0) {
            // 虚拟分页：在原始限制范围内分页
            return buildVirtualPagedSql(originalSql, databaseType, pageNum, pageSize, virtualLimit);
        }

        // 传统分页：清理原始SQL，移除现有的分页限制
        String cleanSql = removePageLimitFromSql(originalSql);

        // 计算分页参数
        int offset = (pageNum - 1) * pageSize;

        // 根据数据库类型构建分页SQL
        if (MyConstant.SQLSERVER.equalsIgnoreCase(databaseType)) {
            // SQL Server分页：使用ROW_NUMBER()方式兼容老版本
            String orderByClause = extractOrderByClause(cleanSql);
            String sqlWithoutOrderBy = removeOrderByClause(cleanSql);

            if (orderByClause.isEmpty()) {
                // 如果没有ORDER BY，使用第一列排序以确保结果一致性
                orderByClause = "ORDER BY 1";
            }

            // 使用ROW_NUMBER()方式，兼容SQL Server 2005+
            return "SELECT TOP " + pageSize + " * FROM " +
                    "(SELECT ROW_NUMBER() OVER(" + orderByClause + ") AS rownum, * FROM (" +
                    sqlWithoutOrderBy + ") AS subquery) AS numbered WHERE rownum > " + offset;
        } else if (MyConstant.MYSQL.equalsIgnoreCase(databaseType)) {
            // MySQL分页：使用LIMIT OFFSET
            return "SELECT * FROM (" + cleanSql + ") AS subquery LIMIT " + offset + ", " + pageSize;
        } else {
            throw new RuntimeException("不支持的数据库类型进行分页：" + databaseType);
        }
    }

    /**
     * 提取SQL中的ORDER BY子句 - 改进版本，避免重复
     */
    private String extractOrderByClause(String sql) {
        // 匹配最后一个ORDER BY子句，避免匹配子查询中的ORDER BY
        Pattern pattern = Pattern.compile("\\s+ORDER\\s+BY\\s+([^()]+?)(?:\\s*$)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(sql);
        if (matcher.find()) {
            String orderBy = matcher.group().trim();
            log.debug("提取到ORDER BY子句: {}", orderBy);
            return orderBy;
        }
        return "";
    }

    /**
     * 获取数据源配置
     */
    private Map<String, String> getSourceConfig(String databaseId, String tid) {
        if (isBlank(databaseId)) {
            return utsDatabaseService.getLocalSourceConfig();
        } else {
            UtsDatabasePojo databaseInfo = utsDatabaseService.getEntity(databaseId, tid);
            Map<String, String> configMap = new HashMap<>();
            configMap.put("driverName", isBlank(databaseInfo.getDriverclassname()) ? "com.mysql.cj.jdbc.Driver" : databaseInfo.getDriverclassname());
            configMap.put("jdbcUrl", databaseInfo.getUrl());
            configMap.put("username", databaseInfo.getUsername());
            configMap.put("password", databaseInfo.getPassword());
            return configMap;
        }
    }

    /**
     * 获取数据源类型
     */
    private String getSourceType(String databaseId, String tid) {
        if (isBlank(databaseId)) {
            return JdbcConstants.MYSQL;
        } else {
            UtsDatabasePojo databaseInfo = utsDatabaseService.getEntity(databaseId, tid);
            String jdbcUrl = databaseInfo.getUrl();
            return (jdbcUrl != null && jdbcUrl.contains("sqlserver")) ? JdbcConstants.SQL_SERVER : JdbcConstants.MYSQL;
        }
    }

    /**
     * 内存分页降级方案
     */
    private Object fallbackToMemoryPaging(List<JSONObject> resultSqlOrHttp, int pageNum, int pageSize) {
        log.warn("降级使用内存分页");
        int offset = (pageNum - 1) * pageSize;

        List<Map<String, Object>> resultList = new ArrayList<>();
        for (JSONObject jsonObject : resultSqlOrHttp) {
            resultList.add(jsonObject.getInnerMap());
        }

        int total = resultList.size();
        if (offset >= total) {
            return buildPageInfo(Collections.emptyList(), total, pageNum, pageSize);
        }

        int end = Math.min(offset + pageSize, total);
        List<Map<String, Object>> paginatedList = resultList.subList(offset, end);
        return buildPageInfo(paginatedList, total, pageNum, pageSize);
    }

    /**
     * 获取SQL查询的总记录数 - 优化版本，支持虚拟分页
     *
     * @param sql          原始SQL查询
     * @param dataSource   数据源
     * @param databaseType 数据库类型
     * @return 总记录数
     */
    private int getTotalCount(String sql, javax.sql.DataSource dataSource, String databaseType) {
        // 首先去除SQL末尾的分号
        String cleanSql = sql.trim();
        if (cleanSql.endsWith(";")) {
            cleanSql = cleanSql.substring(0, cleanSql.length() - 1);
        }

        // 检查原始SQL是否包含TOP/LIMIT限制，如果有则使用虚拟分页
        int virtualLimit = extractVirtualLimit(cleanSql, databaseType);
        if (virtualLimit > 0) {
            log.info("检测到原始SQL包含{}限制，使用虚拟分页，虚拟总数: {}",
                    MyConstant.SQLSERVER.equalsIgnoreCase(databaseType) ? "TOP" : "LIMIT", virtualLimit);
            return virtualLimit;
        }

        // 如果没有TOP/LIMIT限制，执行传统的COUNT查询
        // 如果SQL已经包含分页，需要去除分页部分
        cleanSql = removePageLimitFromSql(cleanSql);

        // 构建COUNT查询 - 优化：移除ORDER BY以提高COUNT性能
        cleanSql = removeOrderByClause(cleanSql);

        String countSql;
        if (MyConstant.SQLSERVER.equalsIgnoreCase(databaseType)) {
            // SQL Server的COUNT查询
            countSql = "SELECT COUNT(*) AS total FROM (" + cleanSql + ") AS count_table";
        } else {
            // MySQL等其他数据库
            countSql = "SELECT COUNT(*) FROM (" + cleanSql + ") AS count_table";
        }

        log.info("执行总数查询SQL: {}", countSql);

        // 使用与executeRelationalDb相同的连接方式
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
        jdbcTemplate.getJdbcTemplate().setQueryTimeout(60);

        try {
            long startTime = System.currentTimeMillis();
            Integer count = jdbcTemplate.queryForObject(countSql, new MapSqlParameterSource(), Integer.class);
            long endTime = System.currentTimeMillis();

            int totalCount = count != null ? count : 0;
            log.info("总数查询完成，耗时：{}ms，总记录数：{}", (endTime - startTime), totalCount);
            return totalCount;
        } catch (Exception e) {
            log.error("总数查询失败: {}", e.getMessage(), e);
            return -1; // 返回-1表示查询失败
        }
    }

    /**
     * 移除SQL中的ORDER BY子句以优化COUNT查询性能 - 改进版本
     */
    private String removeOrderByClause(String sql) {
        // 移除最外层的ORDER BY，但保留子查询中的ORDER BY
        // 改进正则表达式，更准确地匹配ORDER BY子句
        Pattern pattern = Pattern.compile("\\s+ORDER\\s+BY\\s+([^()]+?)(?:\\s*$)", Pattern.CASE_INSENSITIVE);
        String result = pattern.matcher(sql).replaceAll("");
        log.debug("移除ORDER BY后的SQL: {}", result);
        return result;
    }

    /**
     * 从SQL中移除分页语句
     *
     * @param sql 原始SQL
     * @return 去除分页部分的SQL
     */
    private String removePageLimitFromSql(String sql) {
        // 确保SQL不包含分号
        sql = sql.trim();
        if (sql.endsWith(";")) {
            sql = sql.substring(0, sql.length() - 1);
        }

        // 去除MySQL的LIMIT语句
        sql = sql.replaceAll("(?i)\\s+LIMIT\\s+\\d+(\\s*,\\s*\\d+)?\\s*$", "");

        // 去除SQL Server的TOP语句
        sql = sql.replaceAll("(?i)SELECT\\s+TOP\\s+\\d+", "SELECT ");

        // 去除SQL Server的OFFSET FETCH语句
        sql = sql.replaceAll("(?i)\\s+OFFSET\\s+\\d+\\s+ROWS\\s+FETCH\\s+NEXT\\s+\\d+\\s+ROWS\\s+ONLY", "");

        // 去除旧版SQL Server分页（ROW_NUMBER方式）
        if (sql.toUpperCase().contains("ROW_NUMBER() OVER") &&
                sql.toUpperCase().contains("WHERE ROWNUM BETWEEN")) {

            // 尝试提取内部查询
            Pattern pattern = Pattern.compile("FROM\\s+\\((.+?)\\)\\s+AS\\s+InnerQuery", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher matcher = pattern.matcher(sql);

            if (matcher.find()) {
                return matcher.group(1).trim();
            }
        }

        return sql;
    }

    /**
     * 提取原始SQL中的虚拟限制数量（TOP/LIMIT）
     *
     * @param sql          原始SQL
     * @param databaseType 数据库类型
     * @return 限制数量，如果没有限制返回0
     */
    private int extractVirtualLimit(String sql, String databaseType) {
        if (isBlank(sql)) {
            return 0;
        }

        String upperSql = sql.trim().toUpperCase();

        if (MyConstant.SQLSERVER.equalsIgnoreCase(databaseType)) {
            // SQL Server: 匹配 SELECT TOP 数字
            Pattern topPattern = Pattern.compile("SELECT\\s+TOP\\s+(\\d+)", Pattern.CASE_INSENSITIVE);
            Matcher topMatcher = topPattern.matcher(sql);
            if (topMatcher.find()) {
                try {
                    return Integer.parseInt(topMatcher.group(1));
                } catch (NumberFormatException e) {
                    log.warn("解析TOP数量失败: {}", topMatcher.group(1));
                }
            }
        } else if (MyConstant.MYSQL.equalsIgnoreCase(databaseType)) {
            // MySQL: 匹配 LIMIT 数字 或 LIMIT offset, 数字
            Pattern limitPattern = Pattern.compile("LIMIT\\s+(?:\\d+\\s*,\\s*)?(\\d+)\\s*$", Pattern.CASE_INSENSITIVE);
            Matcher limitMatcher = limitPattern.matcher(sql);
            if (limitMatcher.find()) {
                try {
                    return Integer.parseInt(limitMatcher.group(1));
                } catch (NumberFormatException e) {
                    log.warn("解析LIMIT数量失败: {}", limitMatcher.group(1));
                }
            }
        }

        return 0;
    }

    /**
     * 构建虚拟分页SQL - 在原始TOP/LIMIT限制范围内分页
     *
     * @param originalSql  原始SQL
     * @param databaseType 数据库类型
     * @param pageNum      页码
     * @param pageSize     页大小
     * @param virtualLimit 虚拟限制数量
     * @return 分页SQL
     */
    private String buildVirtualPagedSql(String originalSql, String databaseType, int pageNum, int pageSize, int virtualLimit) {
        // 计算分页参数
        int offset = (pageNum - 1) * pageSize;

        // 确保不超过虚拟限制
        if (offset >= virtualLimit) {
            // 超出范围，返回空结果查询
            if (MyConstant.SQLSERVER.equalsIgnoreCase(databaseType)) {
                return "SELECT TOP 0 * FROM (" + originalSql + ") AS empty_result";
            } else {
                return "SELECT * FROM (" + originalSql + ") AS empty_result LIMIT 0";
            }
        }

        // 调整页大小，确保不超过虚拟限制
        int actualPageSize = Math.min(pageSize, virtualLimit - offset);

        if (MyConstant.SQLSERVER.equalsIgnoreCase(databaseType)) {
            return buildSqlServerVirtualPagedSql(originalSql, offset, actualPageSize);
        } else if (MyConstant.MYSQL.equalsIgnoreCase(databaseType)) {
            return buildMySqlVirtualPagedSql(originalSql, offset, actualPageSize);
        } else {
            throw new RuntimeException("不支持的数据库类型进行虚拟分页：" + databaseType);
        }
    }

    /**
     * 构建SQL Server虚拟分页SQL - 兼容2008/2012/2016等版本
     */
    private String buildSqlServerVirtualPagedSql(String originalSql, int offset, int pageSize) {
        // 提取ORDER BY子句
        String orderByClause = extractOrderByClause(originalSql);

        if (orderByClause.isEmpty()) {
            // 如果原始SQL没有ORDER BY，使用第一列排序确保结果一致性
            orderByClause = "ORDER BY 1";
        }

        // 移除原始SQL中的ORDER BY（因为要在外层使用）
        String sqlWithoutOrderBy = removeOrderByClause(originalSql);

        // 使用ROW_NUMBER()方式，兼容SQL Server 2005+（包括2008/2012/2016）
        return "SELECT TOP " + pageSize + " * FROM " +
                "(SELECT ROW_NUMBER() OVER(" + orderByClause + ") AS rownum, * FROM (" +
                sqlWithoutOrderBy + ") AS subquery) AS numbered WHERE rownum > " + offset;
    }

    /**
     * 构建MySQL虚拟分页SQL
     */
    private String buildMySqlVirtualPagedSql(String originalSql, int offset, int pageSize) {
        // MySQL使用子查询 + LIMIT OFFSET方式
        return "SELECT * FROM (" + originalSql + ") AS subquery LIMIT " + offset + ", " + pageSize;
    }


    // dynSentence是自定义的完整SQL，不传的话以freeReportsDB.getDynsentence()为准
    private List<JSONObject> getResultSqlOrHttp(UtsFreereportsPojo freeReportsDB, String dynSentence, String datapath, Map<String, Object> httpPostParams, String tid) {
        String dynType = freeReportsDB.getDyntype();//查询动态类型：sql、http
        if (isBlank(dynSentence)) {
            dynSentence = freeReportsDB.getDynsentence();//查询动态语句：sql语句或者http接口地址
        }
        //// 如果有授权码AuthCode，则在header中追加"authcode":"b8"
        //String authCode = freeReportsDB.getAuthcode();
        //if (isNotBlank(authCode) && isNotBlank(dynSentence)) {
        //    JSONObject dynSentenceJson = JSON.parseObject(dynSentence);
        //    String headerStr = dynSentenceJson.getString("header");
        //
        //    if (isNotBlank(headerStr)) {
        //        JSONObject headerJson = JSON.parseObject(headerStr);
        //        if (isNotBlank(authCode)) {
        //            headerJson.put("authcode", authCode);
        //        }
        //        dynSentenceJson.put("header", headerJson.toJSONString());
        //    }
        //    // 你可以根据需要将处理后的 dynSentenceJson 赋值回 dto 或者进行其他操作
        //    freeReportsDB.setDynsentence(dynSentenceJson.toJSONString());
        //}
        // 将 httpPostParams 替换为 body
        if (MapUtils.isNotEmpty(httpPostParams) && "http".equals(dynType)) {
            JSONObject dynSentenceJson = JSON.parseObject(dynSentence);
            JSONObject bodyJson = new JSONObject(httpPostParams);
            dynSentenceJson.put("body", bodyJson.toJSONString());
            freeReportsDB.setDynsentence(dynSentenceJson.toJSONString());
            dynSentence = dynSentenceJson.toJSONString();
        }

        String databaseid = freeReportsDB.getDatabaseid();
        Integer localMark = freeReportsDB.getLocalmark();// 1:连接本地数据库
        if ("sql".equals(dynType) && isBlank(databaseid) && localMark == 0) {
            throw new BaseBusinessException("未绑定数据库连接");
        }
        UtsDatabasePojo databaseInfo = null;
        //1.获取数据源 sql或http
        DataSource dataSource = new DataSource();
        // 组装sql配置信息  格式：{driverName:com.mysql.cj.jdbc.Driver,jdbcUrl:********************************,username:root,password:123456}
        Map<String, String> configMap = new HashMap<>();
        if (SetTypeEnum.HTTP.getCodeValue().equals(dynType)) {
            //http不需要数据源，兼容已有的逻辑，将http所需要的数据塞进DataSource
            dataSource.setSourceconfig(dynSentence);
            dataSource.setSourcetype(JdbcConstants.HTTP);
            String body = JSONObject.parseObject(dynSentence).getString("body");
            if (isNotBlank(body)) {
                dynSentence = body;
            } else {
                dynSentence = "{}";
            }

        } else { // SQL类型：直接从数据库获取保存的数据源


            if (Objects.equals(localMark, 1)) {
                // 本地连接
                configMap = utsDatabaseService.getLocalSourceConfig();
            } else {
                databaseInfo = utsDatabaseService.getEntity(databaseid, tid);
                configMap.put("driverName", isBlank(databaseInfo.getDriverclassname()) ? "com.mysql.cj.jdbc.Driver" : databaseInfo.getDriverclassname());
                configMap.put("jdbcUrl", databaseInfo.getUrl());
                configMap.put("username", databaseInfo.getUsername());
                configMap.put("password", databaseInfo.getPassword());
            }
            dataSource.setSourceconfig(JSON.toJSONString(configMap));
            // 修复数据库类型识别：根据jdbcUrl动态判断数据库类型，而不是写死MYSQL
            String jdbcUrl = configMap.get("jdbcUrl");
            String sourceType = JdbcConstants.MYSQL; // 默认MySQL
            if (jdbcUrl != null && jdbcUrl.contains("sqlserver")) {
                sourceType = JdbcConstants.SQL_SERVER;
            }
            dataSource.setSourcetype(sourceType);
            dataSource.setId(databaseid);
        }
        // 根据select...from...拼接的只支持mysql和sqlserver
        if (isBlank(dynSentence)) {
            String url = configMap.get("jdbcUrl");
            // 数据库类型:url包含mysql就是mysql数据库,包含sqlserver就是sqlserver数据库 默认mysql
            String databaseType = MyConstant.MYSQL;
            if (url.contains(MyConstant.SQLSERVER)) {
                databaseType = MyConstant.SQLSERVER;
            }
            // 拼接完赋值给dynSentence
            dynSentence = buildSqlFull(freeReportsDB, databaseType);
        }

        //3.参数替换
        //4.获取数据
        DataSourceDto dataSourceDto = new DataSourceDto();
//        BeanUtils.copyProperties(dataSource, dataSourceDto);
        BeanUtils.copyProperties(dataSource, dataSourceDto);
        dataSourceDto.setDynSentence(dynSentence);
        //dataSourceDto.setContextData(setContextData(dataSetParamDtoList));

        // 5.执行查询(sql或http)
        List<JSONObject> data = utsDatabaseService.execute(dataSourceDto);
        PrintColor.red("最终执行【" + dataSourceDto.getSourcetype() + "】查询-->SourceConfig:" + dataSourceDto.getSourceconfig());
        //PrintColor.red("执行返回：" + data);
        ////5.数据转换
        //List<JSONObject> transform = saReportdatasettransformService.transform(dto.getDataSetTransformDtoList(), data);
        /*
          从resultData列表中提取指定路径的数据。
             举例：[{"code":200,"data":{"lister":"张三","username":"nanno"}}]
             若path为"data"，则返回[{"lister":"张三","username":"nanno"}]
             若path为"data.username"，则返回["data.username""nanno"]
             若path为"code"，则返回["code":200]
             如果路径无效或数据不存在则返回null。
         */
        if (isNotBlank(datapath)) {//有DataPath,则分析路径返回,否则返回原数据
            List<Object> transformByDataPath = analyzeDataPath(data, datapath);
            if (transformByDataPath != null) {
                // 将 List<Object> 转换为 List<JSONObject>
                data = transformByDataPath.stream()
                        .filter(obj -> obj instanceof JSONObject)
                        .map(obj -> (JSONObject) obj)
                        .collect(Collectors.toList());
            }
        }
        return data;
    }

    public String replaceSQL(String dynSentence, Map<String, Object> params) {
        if (MapUtils.isNotEmpty(params)) {
            StringBuilder dynSentenceBuilder = new StringBuilder(dynSentence);

            for (Map.Entry<String, Object> entry : params.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                String value = entry.getValue().toString();

                // 处理特殊的 key
                if ("filterstr".equals(entry.getKey())) {
                    dynSentenceBuilder = new StringBuilder(dynSentenceBuilder.toString().replace(placeholder, value));
                } else {
                    dynSentenceBuilder = new StringBuilder(dynSentenceBuilder.toString().replace(placeholder, "'" + value + "'"));
                }
            }

            return dynSentenceBuilder.toString();
        }
        return dynSentence;
    }

    public PageInfo<Map<String, Object>> pageList(String reportcode, QueryParam queryParam, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        Integer pageNum = queryParam.getPageNum();
        Integer pageSize = queryParam.getPageSize();
        String filterStr = queryParam.getFilterstr();//过滤条件:场景
        UtsFreereportsPojo freeReportDB = utsFreereportsMapper.getEntityByReportcode(reportcode, tid);

        String databaseid = freeReportDB.getDatabaseid();
        Integer localMark = freeReportDB.getLocalmark();// 1:连接本地数据库
        if (isBlank(databaseid) && localMark == 0) throw new BaseBusinessException("未绑定数据库连接");
        if (localMark == 1) {
            databaseid = null; // databaseid传null表示连接本地数据库   localMark == 1场景拼tid: 主表名.Tenantid='8b'
            if (isBlank(freeReportDB.getMaintable())) {
                throw new BaseBusinessException("本地连接主表名不能为空MainTable");
            }
            filterStr += " and " + freeReportDB.getMaintable() + ".Tenantid='" + tid + "'";
        }

        Map<String, Object> dataSourceAndName = utsDatabaseService.getDataSource(databaseid, tid);
        javax.sql.DataSource dataSource = (javax.sql.DataSource) dataSourceAndName.get("dataSource");
        // String databaseName = (String) dataSourceAndName.get("databaseName");
        String databaseType = (String) dataSourceAndName.get("databaseType");
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);

        MapSqlParameterSource parameters = new MapSqlParameterSource();
        // 获取自由报表本身拼接的完整SQL 如果DynType是sql 则判断DynSentence是否有完整SQL语句
        String sqlFull = "";
        String dynType = freeReportDB.getDyntype();
        String dynSentence = freeReportDB.getDynsentence();
        if ("sql".equals(dynType) && isNotBlank(dynSentence)) {
            sqlFull = dynSentence;
        } else if ("http".equals(dynType)) {
            // TODO 如果DynType是http 则调用http接口获取数据
        } else {
            sqlFull = buildSqlFull(freeReportDB, databaseType);
        }

        // 客户id过滤: 原始SQL: where groupid in (${groupids})
        // 替换${groupid}为当前用户的Groupids: 'aa','ss','dd'
        if (sqlFull.contains("${groupids}")) {
            String groupids = loginUser.getGroupids();
            if (isBlank(groupids)) throw new BaseBusinessException("当前用户没有关联客户${groupids}");
            sqlFull = sqlFull.replace("${groupids}", groupids);
        }
        // 20240607 Dms/Scm/Rms用户:loginUser加入数据标签DataLabel过滤
        if (sqlFull.contains("${datalabel}")) {
            String dataLabel = loginUser.getDatalabel();
            if (isBlank(dataLabel)) throw new BaseBusinessException("当前用户没有关联数据标签${datalabel}");
            sqlFull = sqlFull.replace("${datalabel}", dataLabel);
        }


        PrintColor.red("sqlFull: " + sqlFull);
        // 最終由jdbc执行的查询SQL
        String sqlResult;
        // 分页偏移量、限制条数
        int offset = (pageNum - 1) * pageSize;
        int limit = pageSize;
        // 处理MySQL、SQLServer的分页查询SQL
        if (MyConstant.MYSQL.equalsIgnoreCase(databaseType)) {
            sqlResult = "SELECT * FROM (" + sqlFull + ") AS subquery LIMIT " + offset + ", " + limit;
        } else if (MyConstant.SQLSERVER.equalsIgnoreCase(databaseType)) {
            sqlResult = "SELECT TOP " + limit + " * FROM " +
                    "(SELECT ROW_NUMBER() OVER(ORDER BY (SELECT NULL)) AS rownum," +
                    " * FROM (" + sqlFull + ") AS subquery) AS numbered" +
                    " WHERE rownum > " + offset;
        } else {
            throw new BaseBusinessException("不支持的数据库类型：" + databaseType);
        }
        // 执行SQL
        List<Map<String, Object>> resultMaps = jdbcTemplate.queryForList(sqlResult, parameters);
        // 获取总记录数
        Integer total = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM (" + sqlFull + ") AS total", parameters, Integer.class);
        PrintColor.zi("jdbcTemplate.queryForList打印实际执行语句: " + sqlResult);
        PrintColor.lan("resultMaps的长度: " + resultMaps.size() + "     分页的total: " + total);
        // 开始构建 PageInfo 对象，并设置分页信息
//            PageInfo<Map<String, Object>> pageInfo = buildPageInfo(resultMaps, total, pageNum, pageSize);
        return buildPageInfo(resultMaps, total, pageNum, pageSize);
    }


    // 开始构建 PageInfo 对象，并设置分页信息
    public static @NotNull PageInfo<Map<String, Object>> buildPageInfo(List<Map<String, Object>> resultMaps, Integer total, Integer pageNum, Integer pageSize) {
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        pageInfo.setList(resultMaps);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((int) Math.ceil((double) total / pageSize));
        pageInfo.setSize(resultMaps.size());
        pageInfo.setStartRow((long) (pageNum - 1) * pageSize + 1);
        pageInfo.setEndRow((long) (pageNum - 1) * pageSize + resultMaps.size());
        pageInfo.setPrePage(pageNum > 1 ? pageNum - 1 : 0);
        pageInfo.setNextPage(pageNum < pageInfo.getPages() ? pageNum + 1 : 0);
        pageInfo.setIsFirstPage(pageNum == 1);
        pageInfo.setIsLastPage(pageNum == pageInfo.getPages());
        pageInfo.setHasPreviousPage(pageNum > 1);
        pageInfo.setHasNextPage(pageNum < pageInfo.getPages());
        pageInfo.setNavigatePages(Math.min(pageInfo.getPages(), 8));

        // 设置导航页码
        List<Integer> navigatepageNumsList = new ArrayList<>();
        int navigateFirstPage = 1;
        int navigateLastPage = pageInfo.getPages();
        int navigatePageNum = pageNum;
        int navigatePages = pageInfo.getNavigatePages();
        if (pageInfo.getPages() > navigatePages) {
            if (navigatePageNum <= navigatePages / 2 + 1) {
                navigateLastPage = navigatePages;
            } else if (navigatePageNum >= pageInfo.getPages() - navigatePages / 2) {
                navigateFirstPage = pageInfo.getPages() - navigatePages + 1;
            } else {
                navigateFirstPage = navigatePageNum - navigatePages / 2;
                navigateLastPage = navigateFirstPage + navigatePages - 1;
            }
        }
        for (int i = navigateFirstPage; i <= navigateLastPage; i++) {
            navigatepageNumsList.add(i);
        }
        int[] navigatepageNums = navigatepageNumsList.stream().mapToInt(i -> i).toArray();
        pageInfo.setNavigatepageNums(navigatepageNums);
        pageInfo.setNavigateFirstPage(navigateFirstPage);
        pageInfo.setNavigateLastPage(navigateLastPage);
        return pageInfo;
    }


    public static String buildSqlFull(UtsFreereportsPojo freeReportDB, String databaseType) {
        return buildSqlFull(freeReportDB, databaseType, null); //无场景过滤条件
    }

    // 拼接获取完整的 SQL 语句 SELECT...(TOP)...FROM...WHERE...GROUP BY...HAVING...ORDER BY...(LIMIT)...   filterStr:场景过滤条件
    public static String buildSqlFull(UtsFreereportsPojo freeReportDB, String databaseType, String filterStr) {
        // 获取各个部分的 SQL
        String select = freeReportDB.getSqlselect();
        String from = freeReportDB.getSqlfrom();
        String where = freeReportDB.getSqlwhere();
        if (isNotBlank(filterStr)) {
            where = where + filterStr; // 增加场景过滤条件
        }
        String groupBy = freeReportDB.getSqlgroupby();
        String having = freeReportDB.getSqlhaving();
        String orderBy = freeReportDB.getSqlorderby();
        String limit = freeReportDB.getSqllimit();
        // 注意,为了兼容sqlserver的分页查询(/pageList方法),limit必须有：对应的sqlserver的语法SQL :SELECT TOP 10000000
        // 示例-没有TOP sqlserver分页会报错:SELECT TOP 3 * FROM (SELECT ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS rownum, * FROM (SELECT p_order.billcode, p_order.billdate, l_billtype.code, l_billtype.name FROM p_order join l_billtype on p_order.billkind = l_billtype.id WHERE p_order.billdate between '2018-09-12 23:00:00.000' and '2018-09-19 01:00:00.001' and p_order.billkind = 1 ORDER BY billcode desc) AS subquery) AS numbered WHERE rownum > 10;
        limit = isBlank(limit) ? "10000000" : limit;

        // 拼接查询语句 SQL
        StringBuilder sql = new StringBuilder("SELECT " + select + " FROM " + from);
        if (isNotBlank(where)) {
            sql.append(" WHERE ").append(where);
        }
        if (isNotBlank(groupBy)) {
            sql.append(" GROUP BY ").append(groupBy);
        }
        if (isNotBlank(having)) {
            sql.append(" HAVING ").append(having);
        }
        if (isNotBlank(orderBy)) {
            sql.append(" ORDER BY ").append(orderBy);
        }
        if (isNotBlank(limit)) {
            if (Objects.equals(databaseType, MyConstant.SQLSERVER)) {
                sql.insert(7, "TOP " + limit + " ");  // 在"SELECT "后面插入"TOP "
            } else if (Objects.equals(databaseType, "mysql")) {
                sql.append(" LIMIT ").append(limit);
            }
        }
        return sql.toString();
    }

    // TODO 拆得不对 拆分 完整的SQL为 SELECT...(TOP)...FROM...WHERE...GROUP BY...HAVING...ORDER BY...(LIMIT)...
    public static UtsFreereportsPojo parseSqlFull(String sqlFull) {
        UtsFreereportsPojo freeReportDB = new UtsFreereportsPojo();
        String databaseType = MyConstant.MYSQL;

        Pattern topPattern = Pattern.compile("(?i)SELECT\\s+TOP\\s+\\d+");
        Matcher topMatcher = topPattern.matcher(sqlFull);
        if (topMatcher.find()) {
            databaseType = MyConstant.SQLSERVER;
        }

        try {
            Pattern selectPattern;
            if (Objects.equals(databaseType, MyConstant.SQLSERVER)) {
                selectPattern = Pattern.compile("(?i)SELECT TOP \\d+ (.*?) FROM");
            } else {
                selectPattern = Pattern.compile("(?i)SELECT (.*?) FROM");
            }
            Matcher selectMatcher = selectPattern.matcher(sqlFull);
            if (selectMatcher.find()) {
                freeReportDB.setSqlselect(selectMatcher.group(1).trim());
            }

            Pattern fromPattern = Pattern.compile("(?i)FROM (.*?) WHERE");
            Matcher fromMatcher = fromPattern.matcher(sqlFull);
            if (fromMatcher.find()) {
                freeReportDB.setSqlfrom(fromMatcher.group(1).trim());
            }

            Pattern wherePattern = Pattern.compile("(?i)WHERE (.*?)( GROUP BY| HAVING| ORDER BY| LIMIT|$)");
            Matcher whereMatcher = wherePattern.matcher(sqlFull);
            if (whereMatcher.find()) {
                freeReportDB.setSqlwhere(whereMatcher.group(1).trim());
            }

            Pattern groupByPattern = Pattern.compile("(?i)GROUP BY (.*?)( HAVING| ORDER BY| LIMIT|$)");
            Matcher groupByMatcher = groupByPattern.matcher(sqlFull);
            if (groupByMatcher.find()) {
                freeReportDB.setSqlgroupby(groupByMatcher.group(1).trim());
            }

            Pattern havingPattern = Pattern.compile("(?i)HAVING (.*?)( ORDER BY| LIMIT|$)");
            Matcher havingMatcher = havingPattern.matcher(sqlFull);
            if (havingMatcher.find()) {
                freeReportDB.setSqlhaving(havingMatcher.group(1).trim());
            }

            Pattern orderByPattern = Pattern.compile("(?i)ORDER BY (.*?)( LIMIT|$)");
            Matcher orderByMatcher = orderByPattern.matcher(sqlFull);
            if (orderByMatcher.find()) {
                freeReportDB.setSqlorderby(orderByMatcher.group(1).trim());
            }

            if (Objects.equals(databaseType, MyConstant.MYSQL)) {
                Pattern limitPattern = Pattern.compile("(?i)LIMIT (.*$)");
                Matcher limitMatcher = limitPattern.matcher(sqlFull);
                if (limitMatcher.find()) {
                    freeReportDB.setSqllimit(limitMatcher.group(1).trim());
                }
            } else if (Objects.equals(databaseType, MyConstant.SQLSERVER)) {
                Pattern limitPattern = Pattern.compile("(?i)SELECT TOP (\\d+) ");
                Matcher limitMatcher = limitPattern.matcher(sqlFull);
                if (limitMatcher.find()) {
                    freeReportDB.setSqllimit(limitMatcher.group(1).trim());
                }
            }
        } catch (IllegalStateException e) {
            System.out.println("SQL 语句格式不正确: " + e.getMessage());
        }

        return freeReportDB;
    }

    @Override
    public List<UtsFreereportsPojo> getListBySelf(int domainnum, String userid, String tid) {
        return utsFreereportsMapper.getListBySelf(domainnum, userid, tid);
    }


    @Override
    public List<UtsFreereportsPojo> myFreeReports(String userid, String tid) {
        return utsFreereportsMapper.myFreeReports(userid, tid);
    }

    @Override
    public List<UtsFreereportsitemPojo> getListByReportCode(String reportcode, String tenantid) {
        return utsFreereportsitemMapper.getListByReportCode(reportcode, tenantid);
    }


    @Override
    public List<Map<String, Object>> getSqlSelectFieldList(String key, String tid) {
        UtsFreereportsPojo freeReportsDB = utsFreereportsMapper.getEntity(key, tid);
        String databaseid = freeReportsDB.getDatabaseid();

        Integer localMark = freeReportsDB.getLocalmark();// 1:连接本地数据库
        if (isBlank(databaseid) && localMark == 0) throw new BaseBusinessException("未绑定数据库连接");
        if (localMark == 1) {
            databaseid = null; // databaseid传null表示连接本地数据库   localMark == 1场景拼tid: 主表名.Tenantid='8b'
            if (isBlank(freeReportsDB.getMaintable())) {
                throw new BaseBusinessException("本地连接主表名不能为空MainTable");
            }
        }

        String sqlSelect = freeReportsDB.getSqlselect();
        // 去除字符串 sqlselect 中的所有空白字符，包括空格、制表符、换行符等
        sqlSelect = sqlSelect.replaceAll("\\s+", "");
        // 解析sqlSelect语句中字段列表
        List<String> fieldList = Arrays.asList(sqlSelect.split(","));
        // 解析sqlSelect语句中的表名列表
        Set<String> tableNames = getDistinctTableNamesFromSqlSelect(sqlSelect);
        PrintColor.red("解析sqlSelect语句中字段列表: " + fieldList);
        PrintColor.zi("解析sqlSelect语句中的表名列表: " + tableNames);

        // 如果tableNames为空，则说明sqlSelect语句中没有表名,是单表查询,表名在SqlFrom语句中,赋值给回tableNames
        if (CollectionUtils.isEmpty(tableNames)) {
            String sqlFrom = freeReportsDB.getSqlfrom();
            // sqlFrom可能取值: "Bus_Machining" 或 " Bus_Machining" 或 "Bus_Machining JOIN Bus_MachiningItem"
            // 表名取值: 第一个表名 "Bus_Machining"
            // 去除开头结尾的空格,并按空格拆分,取第一个为表名
            sqlFrom = sqlFrom.trim();
            String[] froms = sqlFrom.split("\\s+");
            String tableName = froms[0];
            tableNames.add(tableName);
            //fieldList加上"表名."的前缀
            fieldList.replaceAll(s -> tableName + "." + s);
            PrintColor.zi("原始为单表,表名在SqlFrom语句中,解析sqlSelect语句中的表名列表: " + tableNames);
            PrintColor.red("原始为单表,表名在SqlFrom语句中,解析sqlSelect语句中字段列表: " + fieldList);
        }

        // 用于存储所有tableNames表的字段信息
        List<Map<String, Object>> fieldInfos = new ArrayList<>();
        // 查询每个表的字段信息
        for (String tableName : tableNames) {
            List<Map<String, Object>> fieldInfo = utsDatabaseService.tableFields(databaseid, tableName, true, tid);
            fieldInfos.addAll(fieldInfo);
        }
        // 对比 fieldInfos 的 "fieldName" 和 fieldList，留下名字一致的字段,并且按照 fieldList 的顺序排序
        List<Map<String, Object>> result = fieldInfos.stream()
                .filter(field -> fieldList.contains(field.get("fieldName")))
                .sorted(Comparator.comparingInt(field -> fieldList.indexOf(field.get("fieldName"))))
                .collect(Collectors.toList());
        // 遍历 result 列表，对每个字段的fieldName去掉"表名."部分(只要字段名)，并存储在 DataPropertyName 中
        for (Map<String, Object> field : result) {
            String fieldName = (String) field.get("fieldName");
            String dataPropertyName = fieldName.substring(fieldName.indexOf('.') + 1); // 获取表名后的部分
            field.put("DataPropertyName", dataPropertyName);
        }
        return result;
    }

    // 获取SQL语句中的表名列表
    // 如: App_Workgroup.GroupUid, App_Workgroup.GroupName, Mat_Goods.GoodsUid,  Bus_Machining.RefNo, Bus_Machining.BillType
    // 得到[Mat_Goods, Bus_Machining, App_Workgroup]
    public static Set<String> getDistinctTableNamesFromSqlSelect(String sqlSelect) {
//        // 去除字符串 sqlselect 中的所有空白字符，包括空格、制表符、换行符等
//        sqlSelect = sqlSelect.replaceAll("\\s+", "");
        // 按逗号拆分
        String[] fields = sqlSelect.split(",");
        // 提取表名并去重
        Set<String> tableNamesSet = new HashSet<>();
        for (String field : fields) {
            if (field.contains(".")) {
                String tableName = field.substring(0, field.indexOf('.'));
                tableNamesSet.add(tableName);
            }
        }
        // 转换为列表
        return tableNamesSet;
    }


    public static void main(String[] args) {
        String sqlFrom = "    Bus_Machining   JOIN Bus_MachiningItem";

        // 去除开头结尾的空格
        sqlFrom = sqlFrom.trim();
        // 按空格拆分
        String[] froms = sqlFrom.split("\\s+");
        // 取第一个表名
        String tableName = froms[0];

        System.out.println("Table name:" + tableName);
    }


    private static void cleanNull(UtsFreereportsPojo utsFreereportsPojo) {
        if (utsFreereportsPojo.getFrtype() == null) utsFreereportsPojo.setFrtype("");
        if (utsFreereportsPojo.getFrgroupid() == null) utsFreereportsPojo.setFrgroupid("");
        if (utsFreereportsPojo.getLocalmark() == null) utsFreereportsPojo.setLocalmark(0);
        if (utsFreereportsPojo.getReportcode() == null) utsFreereportsPojo.setReportcode("");
        if (utsFreereportsPojo.getReportname() == null) utsFreereportsPojo.setReportname("");
        if (utsFreereportsPojo.getDyntype() == null) utsFreereportsPojo.setDyntype("");
        if (utsFreereportsPojo.getDynsentence() == null) utsFreereportsPojo.setDynsentence("");
        if (utsFreereportsPojo.getSqlfull() == null) utsFreereportsPojo.setSqlfull("");
        if (utsFreereportsPojo.getSqlselect() == null) utsFreereportsPojo.setSqlselect("");
        if (utsFreereportsPojo.getSqlfrom() == null) utsFreereportsPojo.setSqlfrom("");
        if (utsFreereportsPojo.getSqlwhere() == null) utsFreereportsPojo.setSqlwhere("");
        if (utsFreereportsPojo.getSqlgroupby() == null) utsFreereportsPojo.setSqlgroupby("");
        if (utsFreereportsPojo.getSqlhaving() == null) utsFreereportsPojo.setSqlhaving("");
        if (utsFreereportsPojo.getSqlorderby() == null) utsFreereportsPojo.setSqlorderby("");
        if (utsFreereportsPojo.getSqllimit() == null) utsFreereportsPojo.setSqllimit("");
        if (utsFreereportsPojo.getMaintable() == null) utsFreereportsPojo.setMaintable("");
        if (utsFreereportsPojo.getEnabledmark() == null) utsFreereportsPojo.setEnabledmark(0);
        if (utsFreereportsPojo.getPublicmark() == null) utsFreereportsPojo.setPublicmark(0);
        if (utsFreereportsPojo.getDatabaseid() == null) utsFreereportsPojo.setDatabaseid("");
        if (utsFreereportsPojo.getDomainnum() == null) utsFreereportsPojo.setDomainnum(0);
        if (utsFreereportsPojo.getUserid() == null) utsFreereportsPojo.setUserid("");
        if (utsFreereportsPojo.getImageindex() == null) utsFreereportsPojo.setImageindex("");
        if (utsFreereportsPojo.getPermissioncode() == null) utsFreereportsPojo.setPermissioncode("");
        if (utsFreereportsPojo.getImagestyle() == null) utsFreereportsPojo.setImagestyle("");
        if (utsFreereportsPojo.getDatepath() == null) utsFreereportsPojo.setDatepath("");
        if (utsFreereportsPojo.getAuthcode() == null) utsFreereportsPojo.setAuthcode("");
        if (utsFreereportsPojo.getCaseresult() == null) utsFreereportsPojo.setCaseresult("");
        if (utsFreereportsPojo.getReporttype() == null) utsFreereportsPojo.setReporttype("");
        if (utsFreereportsPojo.getCharttype() == null) utsFreereportsPojo.setCharttype("");
        if (utsFreereportsPojo.getRownum() == null) utsFreereportsPojo.setRownum(0);
        if (utsFreereportsPojo.getSummary() == null) utsFreereportsPojo.setSummary("");
        if (utsFreereportsPojo.getCreateby() == null) utsFreereportsPojo.setCreateby("");
        if (utsFreereportsPojo.getCreatebyid() == null) utsFreereportsPojo.setCreatebyid("");
        if (utsFreereportsPojo.getCreatedate() == null) utsFreereportsPojo.setCreatedate(new Date());
        if (utsFreereportsPojo.getLister() == null) utsFreereportsPojo.setLister("");
        if (utsFreereportsPojo.getListerid() == null) utsFreereportsPojo.setListerid("");
        if (utsFreereportsPojo.getModifydate() == null) utsFreereportsPojo.setModifydate(new Date());
        if (utsFreereportsPojo.getCustom1() == null) utsFreereportsPojo.setCustom1("");
        if (utsFreereportsPojo.getCustom2() == null) utsFreereportsPojo.setCustom2("");
        if (utsFreereportsPojo.getCustom3() == null) utsFreereportsPojo.setCustom3("");
        if (utsFreereportsPojo.getCustom4() == null) utsFreereportsPojo.setCustom4("");
        if (utsFreereportsPojo.getCustom5() == null) utsFreereportsPojo.setCustom5("");
        if (utsFreereportsPojo.getTenantid() == null) utsFreereportsPojo.setTenantid("");
        if (utsFreereportsPojo.getTenantname() == null) utsFreereportsPojo.setTenantname("");
        if (utsFreereportsPojo.getRevision() == null) utsFreereportsPojo.setRevision(0);
    }

    /**
     * 从resultData列表中提取指定路径的数据。
     *
     * @param resultData 包含JSON对象的列表。不是列表的也要构建一个列表再返回.
     * @param path       要提取数据的路径，例如 "data" 或 "data.username"。
     *                   举例：[{"code":200,"data":{"lister":"张三","username":"nanno"}}]
     *                   若path为"data"，则返回[{"lister":"张三","username":"nanno"}]
     *                   若path为"data.username"，则返回["data.username""nanno"]
     *                   若path为"code"，则返回["code":200]
     *                   如果路径无效或数据不存在则返回null。
     * @return 根据路径提取的数据，如果路径无效或数据不存在则返回null。
     */
    public static List<Object> analyzeDataPath(List<JSONObject> resultData, String path) {
        // 如果resultData或path为空，返回null
        if (resultData == null || resultData.isEmpty() || path == null || path.isEmpty()) {
            return null;
        }

        // 将路径按"."分割成键数组
        String[] keys = path.split("\\.");
        // 用于存储提取的结果
        List<Object> results = new ArrayList<>();

        // 遍历resultData中的每个JSONObject
        for (JSONObject jsonObject : resultData) {
            // 初始时，当前对象为当前的JSONObject
            Object current = jsonObject;

            // 遍历路径中的每个键
            for (String key : keys) {
                // 如果当前对象是JSONObject，从中获取对应键的值
                if (current instanceof JSONObject) {
                    current = ((JSONObject) current).get(key);
                }
                // 如果当前对象是JSONArray，尝试将键解析为索引并获取对应元素
                else if (current instanceof JSONArray) {
                    try {
                        int index = Integer.parseInt(key);
                        current = ((JSONArray) current).get(index);
                    } catch (NumberFormatException e) {
                        // 如果键不是有效的数字索引，路径解析失败，跳出循环
                        current = null;
                        break;
                    }
                }
                // 如果当前对象既不是JSONObject也不是JSONArray，路径解析失败，跳出循环
                else {
                    current = null;
                    break;
                }
            }

            // 如果路径解析成功且当前对象不为空
            if (current != null) {
                // 如果当前对象是JSONObject直接添加到结果列表中,相当于把当前对象map加入List[0]中
                if (current instanceof JSONObject) {
                    results.add(current);
                } else if (current instanceof JSONArray) {// 如果当前对象是JSONArray，则直接放入List<Object> results
                    results.addAll((JSONArray) current);
                } else {
                    // 其他基本类型（如字符串、数字等），将其包装到List中 以[1个键值对]形式添加到结果列表中
                    JSONObject jsonObjectBasic = new JSONObject();
                    jsonObjectBasic.put(path, current);
                    results.add(jsonObjectBasic);
                }
            }
        }

        // 如果结果列表为空，返回null；否则返回结果列表
        return results.isEmpty() ? null : results;
    }

}
