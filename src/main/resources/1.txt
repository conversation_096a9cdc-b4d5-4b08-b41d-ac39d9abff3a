#!/bin/bash

# 定义固定变量
SOURCE_REPO="hub.inksdev.com/inksdev/svc-sa-oms"
DEST_REPO="hub.inksyun.com/inksdev/svc-sa-oms"
USERNAME="ins"
PASSWORD="Aa@19"
LOG_FILE="/var/log/docker-push-records.log"

# 创建日志目录（如果不存在）
mkdir -p "$(dirname "$LOG_FILE")"

# 获取用户输入的版本号
echo "请输入要推送的sa-oms版本号数字："
read VERSION_NUMBER

# 验证输入是否为空
if [ -z "$VERSION_NUMBER" ]; then
    echo "错误: 版本号不能为空"
    exit 1
fi

# 构造标签
TAG="SNAPSHOT-${VERSION_NUMBER}"

# 显示将要处理的镜像信息
echo "========================================="
echo "Docker镜像推送脚本"
echo "========================================="
echo "源镜像  : ${SOURCE_REPO}:${TAG}"
echo "目标镜像: ${DEST_REPO}:${TAG}"
echo "========================================="

# 检查Docker是否安装
echo "检查Docker环境..."
if ! command -v docker &> /dev/null; then
    echo "错误: 未找到Docker命令，请先安装Docker"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [ERROR] Docker未安装，版本${VERSION_NUMBER}" >> "$LOG_FILE"
    exit 1
fi

# 记录开始时间
START_TIME=$(date +%s)
echo "$(date '+%Y-%m-%d %H:%M:%S') [START] 推送版本${VERSION_NUMBER}" >> "$LOG_FILE"

# 步骤1: 拉取源镜像
echo "步骤1: 正在拉取源镜像 ${SOURCE_REPO}:${TAG}..."
if docker pull "${SOURCE_REPO}:${TAG}"; then
    echo "✓ 镜像拉取成功"
else
    echo "✗ 镜像拉取失败"
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    echo "$(date '+%Y-%m-%d %H:%M:%S') [FAILED] 版本${VERSION_NUMBER} 拉取失败 耗时${DURATION}秒" >> "$LOG_FILE"
    exit 1
fi

# 步骤2: 为镜像打新标签
echo "步骤2: 正在为镜像打标签 ${DEST_REPO}:${TAG}..."
if docker tag "${SOURCE_REPO}:${TAG}" "${DEST_REPO}:${TAG}"; then
    echo "✓ 镜像标签成功"
else
    echo "✗ 镜像标签失败"
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    echo "$(date '+%Y-%m-%d %H:%M:%S') [FAILED] 版本${VERSION_NUMBER} 标签失败 耗时${DURATION}秒" >> "$LOG_FILE"
    exit 1
fi

# 步骤3: 登录目标仓库
echo "步骤3: 正在登录目标仓库 hub.inksyun.com..."
if docker login --username="${USERNAME}" --password="${PASSWORD}" hub.inksyun.com; then
    echo "✓ 仓库登录成功"
else
    echo "✗ 仓库登录失败"
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    echo "$(date '+%Y-%m-%d %H:%M:%S') [FAILED] 版本${VERSION_NUMBER} 登录失败 耗时${DURATION}秒" >> "$LOG_FILE"
    exit 1
fi

# 步骤4: 推送镜像到目标仓库
echo "步骤4: 正在推送镜像到目标仓库..."
if docker push "${DEST_REPO}:${TAG}"; then
    echo "✓ 镜像推送成功"
else
    echo "✗ 镜像推送失败"
    docker logout hub.inksyun.com
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    echo "$(date '+%Y-%m-%d %H:%M:%S') [FAILED] 版本${VERSION_NUMBER} 推送失败 耗时${DURATION}秒" >> "$LOG_FILE"
    exit 1
fi

# 清理登录信息
echo "步骤5: 清理登录信息..."
docker logout hub.inksyun.com

# 步骤6: 删除本地镜像（源镜像和目标镜像）
echo "步骤6: 正在删除本地镜像..."
docker rmi "${SOURCE_REPO}:${TAG}" "${DEST_REPO}:${TAG}" &>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ 本地镜像已清理"
else
    echo "⚠️  部分镜像可能未删除或不存在"
fi

# 记录成功信息和耗时
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
CURRENT_TIME=$(date '+%Y-%m-%d %H:%M:%S')
echo "${CURRENT_TIME} [SUCCESS] 版本${VERSION_NUMBER} 推送成功 耗时${DURATION}秒" >> "$LOG_FILE"

# 步骤7: 发送企微Bot消息
echo "步骤7: 发送企微Bot消息通知..."
WECHAT_MESSAGE="# 🚀 SA-OMS镜像发布通知

## 📦 发布详情
- **版本号**: SNAPSHOT-${VERSION_NUMBER}
- **发布时间**: ${CURRENT_TIME}
- **发布状态**: ✅ 推送成功
- **总耗时**: ${DURATION}秒

## 📋 镜像信息
- **源镜像**: \`${SOURCE_REPO}:${TAG}\`
- **目标镜像**: \`${DEST_REPO}:${TAG}\`

## 🔗 相关链接
[📊 OMS管理后台](http://oms.inksdev.com/#/D01/M03B1)

---
*本次发布已完成，如有问题请及时联系相关负责人*"

# 发送企微消息
WEBHOOK_RESPONSE=$(curl -s -X POST -H "Authorization:admin" -H "Content-Type:application/json" -d "{
  \"content\" : \"${WECHAT_MESSAGE}\",
  \"webhookurl\" : \"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fdef02ed-52e9-48ec-b2f4-7deb58c3e470\"
}" --url "http://localhost:10684/S34M06B1BOT/sendMarkdown")

# 检查企微消息发送结果
if [ $? -eq 0 ]; then
    echo "✓ 企微消息发送成功"
    echo "${CURRENT_TIME} [WECHAT] 版本${VERSION_NUMBER} 企微消息发送成功" >> "$LOG_FILE"
else
    echo "⚠️  企微消息发送失败"
    echo "${CURRENT_TIME} [WECHAT] 版本${VERSION_NUMBER} 企微消息发送失败" >> "$LOG_FILE"
fi

# 输出完成信息
echo "========================================="
echo "🎉 镜像推送完成！"
echo "源镜像  : ${SOURCE_REPO}:${TAG}"
echo "目标镜像: ${DEST_REPO}:${TAG}"
echo "版本号: ${VERSION_NUMBER}"
echo "总耗时: ${DURATION}秒"
echo "========================================="