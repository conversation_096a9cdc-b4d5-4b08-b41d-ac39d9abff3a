<!DOCTYPE html>
<html lang="en" class="dark-theme">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SA-UTS Portal | Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
    <style>
        :root {
            --primary: #6366f1;
            --primary-hover: #4f46e5;
            --bg: #0f172a;
            --surface: #1e293b;
            --surface-hover: #334155;
            --text: #f8fafc;
            --text-secondary: #94a3b8;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --border: #334155;
            --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Cursor Light Effect */
        #cursor-light {
            position: fixed;
            width: 800px;
            height: 800px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.15) 0%, rgba(99, 102, 241, 0) 60%);
            pointer-events: none;
            transform: translate(-50%, -50%);
            z-index: 0;
            transition: opacity 0.3s, transform 0.1s linear;
            opacity: 0;
        }

        body:hover #cursor-light { opacity: 1; }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--bg);
            color: var(--text);
            line-height: 1.6;
            padding: 0;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated Gradient Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--bg);
            z-index: -2;
        }

        /* Digital Rain Background */
        #digital-rain-bg, #fluid-gradient-bg, #particles-js {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        /* Floating Particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 15s infinite linear;
            opacity: 0;
        }

        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.5;
            }
            90% {
                opacity: 0.5;
            }
            100% {
                transform: translateY(-1000px) rotate(720deg);
                opacity: 0;
            }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
            position: relative;
            z-index: 1;
        }

        /* Header Styles */
        header {
            text-align: center;
            margin: 1rem 0 1.5rem;
            position: relative;
            padding: 1rem 0;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: linear-gradient(135deg, var(--primary), #8b5cf6);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px -10px rgba(99, 102, 241, 0.5);
            transform: rotate(45deg);
            transition: var(--transition);
        }

        .logo i {
            font-size: 2.5rem;
            color: white;
            transform: rotate(-45deg);
        }

        h1 {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(90deg, #fff, #a5b4fc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        /* Typing Animation */
        .typing-effect {
            display: inline-block;
            border-right: .15em solid var(--primary);
            white-space: nowrap;
            overflow: hidden;
            animation: typing 2.5s steps(30, end), blink-caret .75s step-end infinite;
        }

        @keyframes typing { from { width: 0 } to { width: 100% } }
        @keyframes blink-caret { from, to { border-color: transparent } 50% { border-color: var(--primary); } }

        header p {
            font-size: 0.9rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
            opacity: 0.8;
            display: none; /* Hide subtitle */
        }

        .header-decoration {
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            filter: blur(60px);
            opacity: 0.15;
            z-index: -1;
        }

        .decoration-1 {
            background: var(--primary);
            top: -50px;
            left: -50px;
            width: 300px;
            height: 300px;
        }

        .decoration-2 {
            background: #8b5cf6;
            bottom: -80px;
            right: -50px;
            width: 250px;
            height: 250px;
        }

        /* Menu Grid */
        .menu {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin: 0 auto;
            max-width: 1200px;
        }

        /* Card Styles */
        .menu-item {
            --mouse-x: 50%;
            --mouse-y: 50%;
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            transition: var(--transition);
            overflow: hidden;
            position: relative;
            box-shadow: var(--shadow);
            transform: translateY(0);
            height: 180px; /* Fixed height for consistency */
            transform-style: preserve-3d;
            will-change: transform;
        }

        /* Interactive Shine Effect */
        .menu-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle 200px at var(--mouse-x) var(--mouse-y), rgba(139, 92, 246, 0.3), transparent 80%);
            opacity: 0;
            transition: opacity 0.3s;
            pointer-events: none;
        }

        .menu-item:hover::after {
            opacity: 1;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            opacity: 0;
            transition: var(--transition);
        }

        .menu-item:hover {
            transform: translateY(-5px) scale(1.03);
            border-color: rgba(99, 102, 241, 0.5);
            box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.3), 0 0 20px rgba(99, 102, 241, 0.3);
        }

        .menu-item:hover::before {
            opacity: 1;
        }

        .menu-item a {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.25rem 1rem;
            color: var(--text);
            text-decoration: none;
            height: 100%;
            position: relative;
            z-index: 1;
            transition: var(--transition);
            text-align: center;
        }

        /* Click feedback */
        .menu-item a:active {
            transform: scale(0.97);
            transition: transform 0.1s;
        }

        .menu-item a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.05));
            opacity: 0;
            transition: var(--transition);
            z-index: -1;
        }

        .menu-item:hover a::before {
            opacity: 1;
        }

        .menu-item .icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.75rem;
            font-size: 1.25rem;
            color: white;
            background: linear-gradient(135deg, var(--primary), #8b5cf6);
            box-shadow: 0 4px 12px -3px var(--primary);
            transition: var(--transition);
        }

        .menu-item:hover .icon {
            transform: scale(1.1) rotate(5deg);
        }

        .menu-item h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: var(--text);
            transition: var(--transition);
        }

        .menu-item p {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin: 0.25rem 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
            padding: 0 0.5rem;
        }

        .menu-item .meta {
            display: none; /* Hide meta info */
        }

        .menu-item .arrow {
            color: var(--primary);
            transition: var(--transition);
            font-size: 1.25rem;
        }

        .menu-item:hover .arrow {
            transform: translateX(5px);
        }

        /* Entrance Animation */
        .menu-item {
            opacity: 0;
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px) scale(0.98); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }

        /* Footer */
        .last-updated {
            text-align: center;
            margin: 4rem auto 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
            padding: 1rem;
            max-width: 600px;
            border-top: 1px solid rgba(255, 255, 255, 0.05);
            position: relative;
        }

        .last-updated::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background: var(--primary);
        }

        /* Loading and No-Files states */
        .loading, .no-files {
            text-align: center;
            padding: 4rem 1rem;
            color: var(--text-secondary);
        }

        .loading i, .no-files i {
            font-size: 2rem;
            margin-bottom: 1rem;
            display: block;
            color: var(--primary);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            h1 {
                font-size: 1.5rem;
            }
            .menu {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
            .menu-item {
                height: 160px;
            }
        }

        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }
        }
    </style>
    <script>
        // Icon mapping for different page types
        const iconMap = {
            'ssh': 'terminal',
            'k8s': 'dharmachakra',
            'kubernetes': 'dharmachakra',
            'pipeline': 'code-branch',
            'management': 'cog',
            'wxe': 'desktop',
            'user': 'users',
            'index': 'home',
            'default': 'file-alt'
        };

        // Get page title from filename
        function getPageTitle(filename) {
            // Remove .html extension and split by hyphens/underscores
            const name = filename.replace(/\.html$/i, '')
                .replace(/[-_]/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase());

            // Special case for WXE

            return name;
        }

        // Get icon based on filename
        function getIcon(filename) {
            const name = filename.toLowerCase();

            for (const [key, icon] of Object.entries(iconMap)) {
                if (name.includes(key)) {
                    return icon;
                }
            }
            return iconMap.default;
        }

        // Get description based on filename
        // Description is now defined in the knownHtmlFiles array above

        // Get version based on filename (this is just for demo - in a real app, you might get this from an API)
        function getVersion() {
            const versions = ['1.0.0', '1.2.3', '2.0.0', '2.1.4', '3.0.0-beta', '3.2.1'];
            return versions[Math.floor(Math.random() * versions.length)];
        }

        // Generate menu item HTML
        function createMenuItem(filename) {
            const title = getPageTitle(filename);
            const icon = getIcon(filename);
            const description = getDescription(filename);

            return `
                <div class="menu-item">
                    <a href="${filename}" target="_blank" rel="noopener noreferrer">
                        <div class="icon">
                            <i class="fas fa-${icon}"></i>
                        </div>
                        <h3>${title}</h3>
                        <p>${description}</p>
                        <div class="meta">
                            <span>v${getVersion()}</span>
                            <span class="arrow">→</span>
                        </div>
                    </a>
                </div>
            `;
        }

        // 增加菜单，只需要在这里加{file: 'xxx.html', description: '描述文本'}就好
        // 只要和all.html同级就会自动加入该html的跳转
        const knownHtmlFiles = [
            {file: 'SSH3.html', description: 'SSH'},
            {file: 'k8s.html', description: 'k8s流水线'},
            {file: 'uts-integration.html', description: 'API整合转发'},
            {file: 'wxe.html', description: '企微审批模版'},
            {file: 'wxeuser.html', description: '企微部门成员'}
        ];

        // Convert to simple array of filenames for compatibility
        const knownHtmlFileNames = knownHtmlFiles.map(item => item.file);

        // Get description for a file
        function getDescription(filename) {
            const item = knownHtmlFiles.find(item => item.file.toLowerCase() === filename.toLowerCase());
            return item ? item.description : `Access the ${getPageTitle(filename)} interface.`;
        }

        // Check if a file exists
        async function fileExists(url) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        // Generate menu with available files
        async function generateMenu() {
            const menu = document.getElementById('menu-grid');
            const loading = document.getElementById('loading-indicator');
            if (!menu || !loading) return;

            const fragment = document.createDocumentFragment();
            let availableFiles = 0;
            let animationDelay = 0;

            for (const fileInfo of knownHtmlFiles) {
                const exists = await fileExists(fileInfo.file);
                if (exists) {
                    availableFiles++;
                    const menuItemHTML = createMenuItem(fileInfo.file);
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = menuItemHTML;
                    const menuItem = tempDiv.firstElementChild;
                    menuItem.style.animationDelay = `${animationDelay}s`;
                    fragment.appendChild(menuItem);
                    animationDelay += 0.07;
                }
            }

            loading.style.display = 'none';
            menu.appendChild(fragment);

            if (availableFiles === 0) {
                const noFiles = document.getElementById('no-files');
                if(noFiles) noFiles.style.display = 'block';
            }

            // After menu is generated, init the tilt effect
            init3dTilt();
        }

        // Initialize 3D tilt effect
        function init3dTilt() {
            const items = document.querySelectorAll('.menu-item');
            items.forEach(item => {
                item.addEventListener('mousemove', (e) => {
                    if (window.matchMedia("(prefers-reduced-motion: reduce)").matches) return;
                    const rect = item.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    const { width, height } = rect;
                    const rotateX = (y / height - 0.5) * -25; // Max rotation
                    const rotateY = (x / width - 0.5) * 25;  // Max rotation
                    item.style.transition = 'transform 0.1s ease-out';
                    item.style.transform = `perspective(1500px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(1.05)`;

                    item.style.setProperty('--mouse-x', `${x}px`);
                    item.style.setProperty('--mouse-y', `${y}px`);
                });
                item.addEventListener('mouseleave', () => {
                    item.style.transition = 'transform 0.5s cubic-bezier(0.23, 1, 0.32, 1)';
                    item.style.transform = 'perspective(1500px) rotateX(0) rotateY(0) scale(1)';
                });
            });
        }

        // Initialize cursor light effect
        function initCursorLight() {
            const light = document.getElementById('cursor-light');
            if (!light || window.matchMedia("(prefers-reduced-motion: reduce)").matches) {
                if(light) light.style.display = 'none';
                return;
            };
            document.addEventListener('mousemove', (e) => {
                light.style.transform = `translate(${e.clientX}px, ${e.clientY}px) translate(-50%, -50%)`;
            });
        }

        // Initialize particles background
        function initParticlesBackground() {
            if (typeof particlesJS === 'undefined') {
                console.error('particles.js not loaded');
                return;
            }
            particlesJS('particles-js', {
                "particles": {
                    "number": {
                        "value": 120,
                        "density": {
                            "enable": true,
                            "value_area": 800
                        }
                    },
                    "color": {
                        "value": "#6366f1"
                    },
                    "shape": {
                        "type": "circle"
                    },
                    "opacity": {
                        "value": 0.5,
                        "random": true,
                        "anim": {
                            "enable": true,
                            "speed": 1,
                            "opacity_min": 0.1,
                            "sync": false
                        }
                    },
                    "size": {
                        "value": 2.5,
                        "random": true,
                        "anim": {
                            "enable": false
                        }
                    },
                    "line_linked": {
                        "enable": true,
                        "distance": 130,
                        "color": "#8b5cf6",
                        "opacity": 0.3,
                        "width": 1
                    },
                    "move": {
                        "enable": true,
                        "speed": 4,
                        "direction": "none",
                        "random": true,
                        "straight": false,
                        "out_mode": "out",
                        "bounce": false,
                        "attract": {
                            "enable": true,
                            "rotateX": 600,
                            "rotateY": 1200
                        }
                    }
                },
                "interactivity": {
                    "detect_on": "canvas",
                    "events": {
                        "onhover": {
                            "enable": true,
                            "mode": "repulse"
                        },
                        "onclick": {
                            "enable": true,
                            "mode": "push"
                        },
                        "resize": true
                    },
                    "modes": {
                        "grab": {
                            "distance": 140,
                            "line_linked": {
                                "opacity": 1
                            }
                        },
                        "repulse": {
                            "distance": 100,
                            "duration": 0.4
                        },
                        "push": {
                            "particles_nb": 4
                        }
                    }
                },
                "retina_detect": true
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            generateMenu();
            initParticlesBackground();
            initCursorLight();
            init3dTilt();

            // Fallback for subtitle
        });
    </script>
</head>
<body>
    <div id="cursor-light"></div>
    <div id="particles-js"></div>

    <div class="container">
        <header>
            <div class="logo"><i class="fas fa-rocket"></i></div>
            <h1 class="typing-effect">SA-UTS Portal | Dashboard</h1>
            <p>Unified Tooling & Services Hub</p>
        </header>

        <main>
            <div class="menu" id="menu-grid">
                <!-- Menu items will be injected here by JavaScript -->
            </div>
            <div id="loading-indicator" class="loading">
                <i class="fas fa-spinner fa-spin"></i> Loading available tools...
            </div>
            <div id="no-files" class="no-files" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i> No HTML files found in this directory.
            </div>
        </main>

        <div class="last-updated">
            Last updated: July 1, 2025 | System Status: <span style="color: #10b981;">All Systems Operational</span>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
</body>
</html>