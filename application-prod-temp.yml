server:
  tomcat:
    uri-encoding: UTF-8
inks:
  license: ${LICENSE_KEY:}
  redisType: mysql
  user:
    wxscan-create: true    #微信公众号扫码可直接创建admin权限用户，默认false
  feign:
    GrfUrl: ${GRFURL:http://dev.inksyun.com:18801}
  # 调用oam公众号接口获取openid #内网测试号:http://192.168.99.96:10677 [wx58c9e35cc9fb9be5] 公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
  oam:
    api: http://oam.inksyun.com
    appid: wx7850d75f765d0dce
  wx:
    cp:
      #      群机器人webhook
      webhookUrl: ${WXE_BOT:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b1271f39-fa4d-487a-9324-1fb42f850e6f}
  tid: ${TID:tid-inks-uts}  # ！！注意同时修改tid和连接的数据库名！！
#spring数据源
spring:
  main:
    allow-bean-definition-overriding: true #允许覆盖bean
  datasource:
    #MYsql连接字符串
    url: jdbc:mysql://${DATABASE_SER:192.168.99.240:53308/inksuts}?useUnicode=true&characterEncoding=utf-8&allowMutilQueries=true&serverTimezone=Asia/Shanghai&useSSL=false
    username: ${DATABASE_USER:root}
    password: ${DATABASE_PD:asd@123456}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 10
  flyway:
    enabled: false   # 禁用Spring Boot自动Flyway配置，使用自定义DataSourceHelper
    initsql: ${INIT_SQL:http://dev.inksyun.com:31080/utils/File/proxy/appsetup/inksuts_init.sql}
    # 发送邮件配置
  mail:
    username: <EMAIL>
    password: ASDqwe@!@#
    host: smtp.qiye.aliyun.com
    default-encoding: UTF-8
    port: 465 # 使用SSL的SMTP端口
    protocol: smtp
    properties:
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true
      mail.smtp.socketFactory.class: javax.net.ssl.SSLSocketFactory
      mail.smtp.socketFactory.port: 465
    toEmail: <EMAIL>
    ipAddress: 96  # IP地址（这个字段与邮件无关）
  web: #配置静态资源访问路径
    resources:
      static-locations: classpath:/static/, classpath:/h5/
  mvc:
    view:
      suffix: .html

mybatis:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.sa.lbl.**.domain
  #配置打印SQL语句到控制台



#雪花算法:  数据中心id,工作机器id
snowflake:
  dataCenterId: 1
  workerId: 1

## MQTT##
mqtt:
  host: tcp://dev.inksyun.com:1883
  username: admin
  password: public
  qos: 2
  clientId: ClientId_96sa-common-uts_#{T(java.util.UUID).randomUUID().toString()}
  timeout: 10
  keepalive: 20
  enableTopic:     # 指定开启监听哪几个topic,逗号分隔如: 1,2 注意:项目启动后需要开启监听几个topic在此方法中定义: connectComplete(boolean reconnect, String serverURI)
  topic1: xapi/home/<USER>
  topic2: /#  #符号是代表整个/下面的全部子主题 不能直接使用#号
  topic3: inks/#
  topic4: $SYS/brokers/+/clients/#
