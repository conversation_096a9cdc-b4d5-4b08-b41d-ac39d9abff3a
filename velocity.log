2025-08-06 09:11:11,157 - Log4<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> initialized using file 'velocity.log'
2025-08-06 09:11:11,158 - Initializing Velocity, Calling init()...
2025-08-06 09:11:11,158 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-08-06 09:11:11,158 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-08-06 09:11:11,158 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-08-06 09:11:11,158 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-08-06 09:11:11,158 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-06 09:11:11,159 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-06 09:11:11,165 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-08-06 09:11:11,180 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-08-06 09:11:11,182 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-08-06 09:11:11,183 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-08-06 09:11:11,183 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-08-06 09:11:11,184 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-08-06 09:11:11,184 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-08-06 09:11:11,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-08-06 09:11:11,186 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-08-06 09:11:11,186 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-08-06 09:11:11,187 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-08-06 09:11:11,200 - Created '20' parsers.
2025-08-06 09:11:11,203 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-08-06 09:11:11,205 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-08-06 09:11:11,205 - Velocimacro : Default library not found.
2025-08-06 09:11:11,205 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-08-06 09:11:11,205 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-08-06 09:11:11,205 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-08-06 09:11:11,205 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-08-06 09:15:33,967 - Log4JLogChute initialized using file 'velocity.log'
2025-08-06 09:15:33,967 - Initializing Velocity, Calling init()...
2025-08-06 09:15:33,967 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-08-06 09:15:33,967 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-08-06 09:15:33,967 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-08-06 09:15:33,967 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-08-06 09:15:33,967 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-06 09:15:33,967 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-06 09:15:33,970 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-08-06 09:15:33,979 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-08-06 09:15:33,982 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-08-06 09:15:33,983 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-08-06 09:15:33,983 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-08-06 09:15:33,984 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-08-06 09:15:33,984 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-08-06 09:15:33,985 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-08-06 09:15:33,986 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-08-06 09:15:33,986 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-08-06 09:15:33,987 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-08-06 09:15:33,999 - Created '20' parsers.
2025-08-06 09:15:34,001 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-08-06 09:15:34,002 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-08-06 09:15:34,002 - Velocimacro : Default library not found.
2025-08-06 09:15:34,002 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-08-06 09:15:34,002 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-08-06 09:15:34,002 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-08-06 09:15:34,002 - Velocimacro : autoload off : VM system will not automatically reload global library macros
