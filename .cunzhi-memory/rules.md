# 开发规范和规则

- k8s.html复制功能在Linux/K8s环境失败，需要添加clipboard API的fallback机制，包含execCommand兼容性处理
- SSH3.html测试连接按钮存在变量引用错误：handleTestConnection函数中使用了未定义的eventOrServerId变量，导致始终使用serverSelect.value，需修复为正确的服务器选择逻辑
- 企业微信消息系统扩展：支持text和markdown两种消息类型，通过数据库msgtype字段动态判断消息格式，兼容现有text消息发送逻辑
- JTDS驱动兼容性修复：JdbcServiceImpl中创建HikariDataSource时必须为JTDS驱动设置connectionTestQuery="SELECT 1"，避免AbstractMethodError异常。JTDS 1.3.1不支持JDBC 4.0的isValid()方法。
